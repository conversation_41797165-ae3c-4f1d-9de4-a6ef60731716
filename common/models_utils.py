import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from multiprocessing import Process, Queue
import optuna
import os
import pandas as pd
import joblib
import multiprocessing
import random
import gc
import traceback
import datetime
import matplotlib.pyplot as plt
os.environ["TF_XLA_FLAGS"] = "--tf_xla_auto_jit=2"
num_threads = str(multiprocessing.cpu_count())
#os.environ["TF_INTRA_OP_PARALLELISM_THREADS"] = num_threads
#os.environ["TF_INTER_OP_PARALLELISM_THREADS"] = num_threads
os.environ["OMP_NUM_THREADS"] = "8"
os.environ["TF_DETERMINISTIC_OPS"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
os.environ["TF_INTRA_OP_PARALLELISM_THREADS"] = "6"
os.environ["TF_INTER_OP_PARALLELISM_THREADS"] = "2"
#os.environ["TF_ENABLE_ONEDNN_OPTS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "4"
os.environ["NUMEXPR_NUM_THREADS"] = "4"
os.environ["MKL_NUM_THREADS"] = "4"

import sys
sys.modules["tensorflow_addons"] = None

import tensorflow as tf
#import tensorflow_probability as tfp
import tensorflow.keras.backend as K
from tensorflow.keras.layers import LSTM, Dense, MultiHeadAttention, Bidirectional, Add, Dropout, Conv1D, MaxPooling1D, Input, Multiply, Softmax, BatchNormalization, Lambda, GlobalAveragePooling1D, LayerNormalization, GlobalMaxPooling1D, Concatenate
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.regularizers import l2
from sklearn.dummy import DummyRegressor
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from tensorflow.keras.models import Model
from common.grafana_utils import GrafanaUtils
from common.mongo_utils import MongoUtils
tf.config.optimizer.set_jit(True)
tf.config.threading.set_intra_op_parallelism_threads(8)
tf.config.threading.set_inter_op_parallelism_threads(2)


SEED = 42
SCALING_Y_FACTOR = 1

random.seed(SEED)
np.random.seed(SEED)
tf.random.set_seed(SEED)
tf.keras.utils.set_random_seed(SEED)


import tracemalloc
from pympler import muppy, summary

tracemalloc.start()


#####################################################################################################
# PROCESS WORKERS
#####################################################################################################

def optimize_model_worker(queue, exec_args):
    def log_to_file(message):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open("/tmp/subprocess_debug.log", "a") as f:
            f.write(f"[{timestamp}] {message}\n")

    try:
        uid = exec_args.get("uid", "unknown")
        #log_to_file(f"🚀 Subprocess started for {uid}")

        # ⚙️ Récupère la config logger
        logger_config = exec_args.get("logger_config", {})
        service = logger_config.get("service", "cryptobot")
        model_instance = ModelsUtils(service=service)

        # 🏃 Lancement de l’optimisation
        score = model_instance.execute_optimize_model_regression_simple_split(**{k: v for k, v in exec_args.items() if k != "logger_config"})

        # 🔍 Récupération des user_attrs depuis trial
        trial = exec_args.get("trial", None)
        user_attrs = dict(trial.user_attrs) if trial and hasattr(trial, "user_attrs") else {}

        queue.put(("success", score, user_attrs))
        #log_to_file(f"✅ Subprocess finished successfully for {uid}")

    except optuna.TrialPruned:
        queue.put(("pruned", None, None))
        #log_to_file(f"⚠️ Trial pruned for {uid}")

    except Exception:
        tb = traceback.format_exc()
        queue.put(("error", tb, None))
        #log_to_file(f"❌ Exception in subprocess for {uid}:\n{tb}")


def train_model_worker(queue, exec_args):

    def log_to_file(message):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open("/tmp/train_debug.log", "a") as f:
            f.write(f"[{timestamp}] {message}\n")

    try:
        uid = exec_args.get("uid", "unknown")
        #log_to_file(f"🚀 Subprocess training started for {uid}")

        logger_config = exec_args.get("logger_config", {})
        service = logger_config.get("service", "cryptobot")
        model_instance = ModelsUtils(service=service)

        # ✅ Corriger ici : retirer logger_config et récupérer les métriques
        metrics = model_instance.execute_training_model(**{
            k: v for k, v in exec_args.items() if k != "logger_config"
        })

        queue.put(("success", metrics))
        #log_to_file(f"✅ Subprocess training finished for {uid}")

    except Exception as e:
        import traceback
        tb = traceback.format_exc()
        queue.put(("error", tb))
        #log_to_file(f"❌ Exception in training subprocess for {uid}:\n{tb}")






class ModelsUtils:
    #####################################################################################################
    # INIT CLASS
    #####################################################################################################
    def __init__(self, logger=None, service="cryptobot"):
        self.logger = GrafanaUtils(service)
        self.mongo = MongoUtils()
        self.mongo.connect(service=service)
        self.set_seed(SEED)

    def set_seed(self,seed=SEED):
        os.environ['PYTHONHASHSEED'] = str(seed)
        random.seed(seed)
        np.random.seed(seed)
        tf.random.set_seed(seed)
        tf.keras.utils.set_random_seed(seed)
        tf.config.experimental.enable_op_determinism()


    #####################################################################################################
    # TOOLS FUNCTION
    #####################################################################################################

    def cleanup_training_memory(self, local_vars: dict, custom_vars: list = None):
        vars_to_delete = [
            "df", "X", "y", "X_scaled", "y_scaled", "X_seq", "y_seq",
            "X_train", "X_val", "y_train", "y_val",
            "y_val_pred", "y_train_pred", "y_val_pred_scaled", "y_train_pred_scaled",
            "y_val_true", "y_train_true",
            "scaler_X", "scaler_y", "history", "metrics"
        ]

        if custom_vars:
            vars_to_delete.extend(custom_vars)

        # 🔥 Libération explicite du modèle
        if "model" in local_vars:
            try:
                model = local_vars["model"]
                model.stop_training = True
                model.__del__()  # libère les poids, graphes, etc.
                del model
            except Exception:
                pass
            finally:
                local_vars.pop("model", None)

        # Suppression manuelle des autres variables
        for var in vars_to_delete:
            if var in local_vars:
                try:
                    del local_vars[var]
                except Exception:
                    pass

        # 🧽 Clear TensorFlow cache
        try:
            tf.keras.backend.clear_session()
            K.clear_session()
        except:
            pass

        gc.collect()

    def log_memory_snapshot(self, uid, context="global"):
        try:
            # Tracemalloc - Top 5 lignes
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')

            self.logger.send_log(f"📊 - {uid} - {context} - Top 5 lignes les plus gourmandes :", "debug")
            for stat in top_stats[:5]:
                self.logger.send_log(f"📍 - {uid} - {context} - {stat}", "debug")

            # Pympler - résumé objets Python
            all_objects = muppy.get_objects()
            sum_obj = summary.summarize(all_objects)

            top_lines = list(summary.format_(sum_obj))
            for line in top_lines[:5]:  # on limite pour éviter le flood
                self.logger.send_log(f"📦 - {uid} - {context} - {line}", "debug")

            # RAM brute (RSS)
            import psutil, os
            mem = psutil.Process(os.getpid()).memory_info().rss / 1024**2
            self.logger.send_log(f"🧠 - {uid} - {context} - RSS total : {mem} MB", "info")

            gc.collect()
        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - {context} - Erreur snapshot memory : {e}", "error")


    def visualize_layer_activations(
        self, model, X_sample, base_log: str, base_path: str, layer_names=None
    ):
        """
        Visualise et enregistre les activations de chaque couche de manière progressive.

        Parameters:
        -----------
        model : tf.keras.Model
            Modèle complet à analyser.
        X_sample : np.array
            Exemple d'entrée (une séquence) à passer dans le modèle.
        base_log : str
            Texte de base pour les logs.
        base_path : str
            Chemin de base pour l'enregistrement des images.
        layer_names : list, optional
            Liste des noms des couches à afficher. Si None, toutes les couches sont affichées.
        """
        import os
        os.makedirs(base_path, exist_ok=True)

        if layer_names is None:
            layer_names = [layer.name for layer in model.layers if len(layer.output_shape) > 1]

        # Extraction des couches par nom
        outputs = [layer.output for layer in model.layers if layer.name in layer_names]
        intermediate_model = tf.keras.Model(inputs=model.input, outputs=outputs)

        # Passage de l'échantillon dans les couches
        activations = intermediate_model.predict(X_sample[np.newaxis, ...])

        # Création de la figure
        fig = plt.figure(figsize=(18, len(activations) * 3))
        fig.suptitle(f"Visualisation des Activations - {base_log}", fontsize=20)
        gs = fig.add_gridspec(len(activations), 1)

        for i, activation in enumerate(activations):
            layer_name = layer_names[i]
            ax = fig.add_subplot(gs[i, 0])
            ax.set_title(f"{layer_name} - Shape : {activation.shape}")

            if len(activation.shape) == 3:  # (Batch, Steps, Features)
                ax.imshow(activation[0].T, aspect='auto', cmap='viridis')
                ax.set_xlabel("Time Steps")
                ax.set_ylabel("Features")
                ax.colorbar = fig.colorbar(ax.imshow(activation[0].T, aspect='auto', cmap='viridis'), ax=ax)

            elif len(activation.shape) == 2:  # (Batch, Features)
                ax.plot(activation[0])
                ax.set_xlabel("Features")
                ax.set_ylabel("Amplitude")

            else:  # (Batch, Single Value)
                ax.text(0.5, 0.5, f"Valeur : {activation[0]}", fontsize=14, ha='center')

        # Sauvegarde de la figure
        save_path = os.path.join(base_path, f"{base_log}_activations.png")
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        plt.savefig(save_path)
        plt.close(fig)

        # Log de confirmation
        self.logger.send_log(f"✅ - {base_log} - Visualisation des activations enregistrée : {save_path}", "info")



    def plot_training_diagnostics(
        self, y_true, y_pred, fold_number: str, base_log: str, base_path: str
    ):
        """
        Affiche et sauve  diagnostics :
        - Distribution log_returns
        - Scatter true vs pred (+R2, corr)
        - Séries temps avec dir_acc
        - Hist erreurs (MAE, RMSE)
        - Scatter erreur vs vrai
        - CDF erreurs absolues
        """
        # 1) Calcul métriques
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2   = r2_score(y_true, y_pred)
        corr = np.corrcoef(y_true, y_pred)[0,1]
        errors = y_true - y_pred
        abs_err = np.abs(errors)
        sorted_err = np.sort(abs_err)
        cdf = np.arange(len(sorted_err)) / len(sorted_err)

        # 2) Directional Accuracy (long/short)
        dir_acc = np.mean(np.sign(y_pred) == np.sign(y_true))

        # 3) Création figure 3x3
        fig = plt.figure(figsize=(18, 16))
        fig.suptitle(f"Diagnostics - Fold {fold_number} - {base_log}", fontsize=20)
        gs = fig.add_gridspec(3, 3, height_ratios=[1, 1.5, 1])

        # Distribution log_returns
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.hist(y_true, bins=50, alpha=0.5, label='True')
        ax1.hist(y_pred, bins=50, alpha=0.5, label='Predicted')
        ax1.set_title('Distribution des log_returns')
        ax1.legend()
        ax1.text(
            0.95, 0.95,
            f"σ_true={np.std(y_true):.4f}\nσ_pred={np.std(y_pred):.4f}",
            transform=ax1.transAxes,
            ha='right', va='top'
        )

        # Scatter True vs Pred
        ax2 = fig.add_subplot(gs[0, 1:])
        ax2.scatter(y_true, y_pred, alpha=0.3)
        ax2.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'k--')
        ax2.set_title(f"Scatter True vs Predicted\nR²={r2:.4f}, Corr={corr:.4f}")

        # Séries temporelle
        ax3 = fig.add_subplot(gs[1, :])
        ax3.plot(y_true, label='True')
        ax3.plot(y_pred, label='Predicted', alpha=0.7)
        ax3.set_title(f"Time Series\nDirAcc={dir_acc:.4f}")
        ax3.legend()

        # Hist des erreurs
        ax4 = fig.add_subplot(gs[2, 0])
        ax4.hist(errors, bins=50, alpha=0.7)
        ax4.set_title(f"Erreurs\nMAE={mae:.4f}, RMSE={rmse:.4f}")

        # Erreur vs valeur vraie
        ax5 = fig.add_subplot(gs[2, 1])
        ax5.scatter(y_true, errors, alpha=0.3)
        ax5.axhline(0, linestyle='--')
        ax5.set_title('Erreur vs True')

        # CDF erreurs absolues
        ax6 = fig.add_subplot(gs[2, 2])
        ax6.plot(sorted_err, cdf)
        ax6.set_title('CDF Erreurs absolues')

        # Sauvegarde
        save_path = f"{base_path}/{base_log}_fold_{fold_number}_diagnostics.png"
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        plt.savefig(save_path)
        plt.close(fig)





    def diagnose_train_val_split(self, y_train, y_val, base_log, trial=None):
        """
        Diagnostique le split Train/Validation.
        Peut automatiquement pruner si le split est mauvais (si trial est fourni).
        Ajuste dynamiquement les seuils selon la variance de y_train.
        """
        try:
            std_train = np.std(y_train)
            std_val = np.std(y_val)
            mean_train = np.mean(y_train)
            mean_val = np.mean(y_val)
            min_train = np.min(y_train)
            min_val = np.min(y_val)
            max_train = np.max(y_train)
            max_val = np.max(y_val)

            std_ratio = std_val / (std_train + 1e-8)
            mean_diff = abs(mean_val - mean_train)

            self.logger.send_log(f"🧪 - {base_log} - Train/Val split stats:", "debug")
            self.logger.send_log(f"🧪 - {base_log} - Train: μ={mean_train:.4f}, σ={std_train:.4f}, min={min_train:.4f}, max={max_train:.4f}", "debug")
            self.logger.send_log(f"🧪 - {base_log} - Val:   μ={mean_val:.4f}, σ={std_val:.4f}, min={min_val:.4f}, max={max_val:.4f}", "debug")
            self.logger.send_log(f"🧪 - {base_log} - → std_ratio (val/train): {std_ratio:.2f}, mean_diff: {mean_diff:.4f}", "debug")

            # 🔵 Ajustement dynamique des seuils
            if std_train < 0.5:  # Target très faible -> être plus tolérant
                std_ratio_min = 0.4
                std_ratio_max = 2.5
                mean_diff_threshold = 1.0 * std_train
            elif std_train < 1.5:
                std_ratio_min = 0.5
                std_ratio_max = 2.0
                mean_diff_threshold = 0.75 * std_train
            else:
                std_ratio_min = 0.6
                std_ratio_max = 1.8
                mean_diff_threshold = 0.6 * std_train

            # 🔍 Avertissements
            if std_ratio > std_ratio_max or std_ratio < std_ratio_min:
                self.logger.send_log(f"⚠️ - {base_log} - Écart-type validation/train déséquilibré (ratio={std_ratio:.2f})", "warning")

            if mean_diff > mean_diff_threshold:
                self.logger.send_log(f"⚠️ - {base_log} - Moyenne validation/train décalée de façon significative", "warning")

            # 🚨 Pruning automatique si demandé
            if hasattr(trial, "study") and trial.study and trial.number > 0:
                if std_ratio < std_ratio_min or std_ratio > std_ratio_max or mean_diff > mean_diff_threshold:
                    self.logger.send_log(f"🚫 - {base_log} - Pruning trial à cause d'un mauvais split (std_ratio={std_ratio:.2f}, mean_diff={mean_diff:.4f})", "warning")
                    raise optuna.TrialPruned()

        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - Erreur dans diagnose_train_val_split : {e}", "error")


    def check_r2_diagnostics(self, y_true, y_pred, X=None, base_log="R2_CHECK"):
        try:
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)

            std_y = np.std(y_true)
            std_y_pred = np.std(y_pred)
            mean_y = np.mean(y_true)
            mean_y_pred = np.mean(y_pred)
            var_y = np.var(y_true)
            var_y_pred = np.var(y_pred)
            ratio_std = std_y_pred / (std_y + 1e-8)
            ratio_var = var_y_pred / (var_y + 1e-8)

            self.logger.send_log(f"σ(y_true) = {std_y:.6f}, σ(y_pred) = {std_y_pred:.6f}, ratio σ = {ratio_std:.2f}")
            self.logger.send_log(f"Var(y_true) = {var_y:.6e}, Var(y_pred) = {var_y_pred:.6e}, ratio var = {ratio_var:.2f}")
            self.logger.send_log(f"Moyenne y_true = {mean_y:.6f}, y_pred = {mean_y_pred:.6f}")

            # Check amplitude
            if ratio_std < 0.5:
                self.logger.send_log("⚠️ Les prédictions sont trop plates par rapport à la variance réelle.")
            elif ratio_std > 2:
                self.logger.send_log("⚠️ Les prédictions sont trop amplifiées.")

            # Check moyenne
            if abs(mean_y_pred - mean_y) > std_y:
                self.logger.send_log("⚠️ Le modèle est décentré : moyenne prédite très différente de la vraie.")

            # Check outliers
            q99 = np.quantile(np.abs(y_true), 0.99)
            if q99 > 0.03:
                self.logger.send_log(f"⚠️ Outliers détectés : 99e percentile des log_returns = {q99:.4f}")

            # Check direction
            sign_acc = np.mean(np.sign(y_true) == np.sign(y_pred))
            self.logger.send_log(f"📈 Accuracité directionnelle : {sign_acc:.2%}")
            if sign_acc < 0.55:
                self.logger.send_log("⚠️ Très peu de cohérence de direction entre y_true et y_pred.")

            # Analyse X si disponible
            if X is not None:
                X_df = pd.DataFrame(X.reshape(X.shape[0], -1)) if X.ndim == 3 else pd.DataFrame(X)
                zero_var_cols = X_df.loc[:, X_df.std() < 1e-5].shape[1]
                self.logger.send_log(f"🧪 Features sans variance : {zero_var_cols}")
                if zero_var_cols > 0:
                    self.logger.send_log("⚠️ Certaines features n'ont aucune variance. À supprimer.")

        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - Erreur dans check_r2_diagnostics : {str(e)}", "warning")


    def keep_only_features(self, df, used_features):
         """
         Conserve uniquement les colonnes spécifiées dans used_features.
         Returns:
             pd.DataFrame: Nouveau DataFrame contenant uniquement les colonnes sélectionnées.
         """
         existing_features = [feature for feature in used_features if feature in df.columns]
         return df[existing_features]


    #####################################################################################################
    # CUSTOM LOSSES FUNCTIONS
    #####################################################################################################

    def compute_delta_huber(self, y_train, multiplier=2.0, min_value=1e-4, max_value=0.1, base_log=""):
        """
        Calcule dynamiquement delta pour la perte Huber en fonction de l'écart-type de y_train.

        Args:
            y_train (np.ndarray): vecteur cible d'entraînement
            multiplier (float): facteur multiplicatif sur l'écart-type
            min_value (float): borne inférieure du delta
            max_value (float): borne supérieure du delta
            logger (Logger): logger personnalisé pour affichage
            base_log (str): préfixe log facultatif

        Returns:
            float: delta_huber borné et adapté à y_train
        """
        std = np.std(y_train)
        delta = multiplier * std
        delta = np.clip(delta, min_value, max_value)

        #self.logger.send_log(f"📐 {base_log} - delta_huber dynamique : σ={std:.6f}, brut={multiplier * std:.6f}, borné={delta:.6f}",level="debug")

        return delta

        # Fonction de perte personnalisée Huber pondérée
    def penalized_huber_loss(self, delta):
        def loss(y_true, y_pred):
            error = tf.abs(y_true - y_pred)
            is_small = error <= delta
            huber = tf.where(is_small,
                            0.5 * tf.square(error),
                            delta * (error - 0.5 * delta))
            weight = tf.clip_by_value(error / delta, 1.0, 10.0)
            return tf.reduce_mean(huber * weight)
        return loss


    def amplitude_directional_loss(self, delta=0.01, alpha=0.7, trend_lag=1, amplitude_weighting=True):
        """
        Combine une pénalité d'amplitude (RMSE pondéré) et une pénalité directionnelle.

        Args:
            alpha (float): poids du terme directionnel
            trend_lag (int): pas de temps pour la tendance locale
            amplitude_weighting (bool): active une pondération plus sévère des grosses erreurs

        Returns:
            Callable loss function
        """
        def loss(y_true, y_pred):
            errors = y_true - y_pred

            if amplitude_weighting:
                # 🔥 Pénalisation exponentielle sur grandes erreurs
                weights = tf.clip_by_value(tf.abs(errors), 1.0, 10.0)
                amplitude_loss = tf.sqrt(tf.reduce_mean(weights * tf.square(errors)) + 1e-8)
            else:
                amplitude_loss = tf.sqrt(tf.reduce_mean(tf.square(errors)) + 1e-8)

            # 🔁 Direction locale
            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]

            trend_match = tf.cast(tf.equal(tf.sign(delta_true), tf.sign(delta_pred)), tf.float32)
            trend_loss = 1.0 - tf.reduce_mean(trend_match)

            return (1 - alpha) * amplitude_loss + alpha * trend_loss

        return loss


    def pearson_trend_amplitude_loss(self, gamma_val=0.7, alpha_val=0.5,
                                    trend_lag=1,
                                    lambda_std_val=10.0,
                                    amplitude_weighting=True):

        def loss(y_true, y_pred):
            # Corrélation
            x = y_true - tf.reduce_mean(y_true, axis=0)
            y = y_pred - tf.reduce_mean(y_pred, axis=0)
            r = tf.reduce_sum(x * y, axis=0) / (
                tf.sqrt(tf.reduce_sum(tf.square(x), axis=0)) *
                tf.sqrt(tf.reduce_sum(tf.square(y), axis=0)) + 1e-8)
            pearson_loss = 1.0 - tf.reduce_mean(r)

            # Amplitude
            errors = y_true - y_pred
            if amplitude_weighting:
                weights = tf.clip_by_value(tf.abs(errors), 1.0, 10.0)
                amplitude_loss = tf.sqrt(tf.reduce_mean(weights * tf.square(errors)) + 1e-8)
            else:
                amplitude_loss = tf.sqrt(tf.reduce_mean(tf.square(errors)) + 1e-8)

            # Trend direction
            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]
            match = tf.cast(tf.equal(tf.sign(delta_true), tf.sign(delta_pred)), tf.float32)
            trend_loss = 1.0 - tf.reduce_mean(match)

            secondary = (1 - alpha_val) * amplitude_loss + alpha_val * trend_loss

            pred_std = tf.math.reduce_std(y_pred)
            true_std = tf.math.reduce_std(y_true)
            std_penalty = tf.square(pred_std - true_std)

            return gamma_val * pearson_loss + (1 - gamma_val) * secondary + lambda_std_val * std_penalty
        return loss


    def dual_objective_loss(self, gamma_val=0.7, alpha_val=0.5,
                        trend_lag=1,
                        lambda_std_val=10.0,
                        lambda_scale_val=10.0,
                        amplitude_weighting=True):

        def loss(y_true, y_pred):
            # --- Corrélation (pour la direction)
            x = y_true - tf.reduce_mean(y_true, axis=0)
            y = y_pred - tf.reduce_mean(y_pred, axis=0)
            r = tf.reduce_sum(x * y, axis=0) / (
                tf.sqrt(tf.reduce_sum(tf.square(x), axis=0)) *
                tf.sqrt(tf.reduce_sum(tf.square(y), axis=0)) + 1e-8)
            pearson_loss = 1.0 - tf.reduce_mean(r)

            # --- Trend Direction (précision sur les signes)
            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]
            trend_match = tf.cast(tf.equal(tf.sign(delta_true), tf.sign(delta_pred)), tf.float32)
            trend_loss = 1.0 - tf.reduce_mean(trend_match)

            # --- Calibration d'Amplitude (cible : β = 1, c = 0)
            pred_std = tf.math.reduce_std(y_pred)
            true_std = tf.math.reduce_std(y_true)
            std_penalty = tf.square((pred_std / (true_std + 1e-8)) - 1.0)

            pred_mean = tf.reduce_mean(y_pred)
            true_mean = tf.reduce_mean(y_true)
            scale_factor = true_std / (pred_std + 1e-8)
            y_calibrated = y_pred * scale_factor + (true_mean - pred_mean * scale_factor)
            calibration_loss = tf.sqrt(tf.reduce_mean(tf.square(y_true - y_calibrated)))

            # --- Combinaison avec pondération dynamique
            amplitude_factor = tf.abs(pred_std - true_std) / (true_std + 1e-8)
            amplitude_loss = lambda_scale_val * calibration_loss * amplitude_factor

            return (gamma_val * trend_loss +
                    (1 - gamma_val) * amplitude_loss +
                    lambda_std_val * std_penalty)

        return loss

    '''
    def adaptive_dual_objective_loss(self,
                                    gamma_val=0.9,
                                    alpha_val=0.5,
                                    trend_lag=1,
                                    lambda_std_val=10.0,
                                    lambda_scale_val=10.0):
        """
        Fonction de perte adaptative avec trois composantes :
        - Direction (trend) : Évalue si les tendances des prédictions suivent les tendances des vraies valeurs.
        - Amplitude (amplitude_loss) : Évalue si les amplitudes des prédictions sont cohérentes avec les vraies valeurs.
        - Écart-type (std_penalty) : Corrige les décalages d'échelle entre les amplitudes des prédictions et des vraies valeurs.

        Args:
            gamma_val (float): Poids de la composante directionnelle par rapport à l'amplitude.
            alpha_val (float): Poids de la composante trend vs amplitude dans l'objectif secondaire.
            trend_lag (int): Nombre de pas pour le calcul de la tendance (différence).
            lambda_std_val (float): Coefficient de pénalisation pour la différence d'écart-type.
            lambda_scale_val (float): Coefficient de pondération pour l'amplitude.

        Returns:
            Callable: Fonction de perte pour la compilation du modèle.
        """
        def loss(y_true, y_pred):
            # --- Corrélation directionnelle globale (Pearson Loss)
            x = y_true - tf.reduce_mean(y_true, axis=0)
            y = y_pred - tf.reduce_mean(y_pred, axis=0)
            r = tf.reduce_sum(x * y, axis=0) / (
                tf.sqrt(tf.reduce_sum(tf.square(x), axis=0)) *
                tf.sqrt(tf.reduce_sum(tf.square(y), axis=0)) + 1e-8)
            pearson_loss = tf.where(r < 0.5, 1.0 - r, 1.0 - tf.reduce_mean(r))

            # --- Trend Direction (tendance locale pondérée)
            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]
            trend_match = tf.cast(tf.equal(tf.sign(delta_true), tf.sign(delta_pred)), tf.float32)
            weighted_trend_loss = 1.0 - tf.reduce_mean(trend_match * tf.abs(delta_true))

            # --- Calibration d'Amplitude (écart-type)
            pred_std = tf.math.reduce_std(y_pred)
            true_std = tf.stop_gradient(tf.math.reduce_std(y_true))
            std_penalty = tf.square((pred_std / (true_std + 1e-8)) - 1.0)

            # --- Amplitude Loss (erreur proportionnelle avec pondération)
            errors = y_true - y_pred
            mean_error = tf.reduce_mean(tf.abs(errors)) + 1e-8
            scaling_factor_value = 5.0
            scaling_factor = tf.where(tf.abs(y_true) < mean_error, scaling_factor_value, 1.0)
            amplitude_loss = tf.sqrt(tf.reduce_mean(scaling_factor * tf.square(errors)) + 1e-8)

            # --- Ajustement directionnel dynamique (pondéré par la magnitude)
            dynamic_direction_loss = (1 - alpha_val) * amplitude_loss + alpha_val * weighted_trend_loss
            amplitude_factor = tf.stop_gradient(tf.abs(pred_std - true_std) / (true_std + 1e-8))
            amplitude_loss = lambda_scale_val * amplitude_loss * amplitude_factor

            # --- Formule finale de la perte
            final_loss = (
                gamma_val * weighted_trend_loss +
                (1 - gamma_val) * dynamic_direction_loss +
                lambda_std_val * std_penalty
            )

            return final_loss

        return loss
    '''

    def adaptive_dual_objective_loss(
            self,
            gamma_val: float = 0.7,          # poids global trend vs. amplitude
            alpha_val: float = 0.3,          # poids local trend vs. amplitude
            trend_lag: int = 1,              # fenêtre pour la tendance locale
            lambda_std_val: float = 50.0,    # pénalité d’écart-type
            lambda_scale_val: float = 20.0,  # pondération d’amplitude
            lambda_sign_val: float = 1.3,
            w_pearson: float = 0.1,          # poids (optionnel) du terme Pearson global
            scaling_factor_value: float = 3.0,   # poids des petites valeurs-cible
        ):
        """
        Loss « 3-en-1 » : direction locale, amplitude, calibration de volatilité,
        avec possibilité d’un terme de corrélation globale adouci.

        Paramètres additionnels
        -----------------------
        w_pearson : float ∈ [0, 1]
            Si >0, on ajoute un terme de corrélation globale (soft-plus) pondéré par w_pearson.
        scaling_factor_value : float
            Facteur (>1) appliqué aux échantillons dont |y_true| est inférieur à l’erreur
            moyenne, afin de ne pas négliger les petits mouvements.
        """

        def loss(y_true, y_pred):
            # ════════════════════════════════════════════════════════════════
            # 1. Corrélation globale (optionnelle, lissée par soft-plus)
            # ════════════════════════════════════════════════════════════════
            x_c = y_true - tf.reduce_mean(y_true)
            y_c = y_pred - tf.reduce_mean(y_pred)
            r = tf.reduce_sum(x_c * y_c) / (
                tf.sqrt(tf.reduce_sum(tf.square(x_c))) * tf.sqrt(tf.reduce_sum(tf.square(y_c))) + 1e-8
            )
            pearson_soft_loss = tf.nn.softplus(-2.0 * (r - 0.5))      # ≈ 0 si r ≳ 0.9

            # ════════════════════════════════════════════════════════════════
            # 2. Tendance locale (trend loss pondéré par magnitude)
            # ════════════════════════════════════════════════════════════════
            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]
            trend_ok = tf.cast(tf.equal(tf.sign(delta_true), tf.sign(delta_pred)), tf.float32)

            delta_len = tf.shape(delta_true)[0]

            def safe_median(v):
                """
                Retourne la médiane de |v|, ou 1.0 si v est vide
                (évite un Gather sur tenseur de taille 0).
                """
                return tf.cond(
                    delta_len > 0,
                    lambda: tf.gather(tf.sort(tf.abs(v)), delta_len // 2),
                    lambda: tf.constant(1.0)
                )

            med_mag = safe_median(delta_true) + 1e-8

            trend_weight = tf.abs(delta_true) / med_mag

            trend_loss = 1.0 - tf.reduce_mean(trend_ok * trend_weight)

            # ════════════════════════════════════════════════════════════════
            # 3. Amplitude loss (RMSE pondéré)
            # ════════════════════════════════════════════════════════════════
            errors = y_true - y_pred
            mean_abs_err = tf.reduce_mean(tf.abs(errors)) + 1e-8
            scaling_mask = tf.where(tf.abs(y_true) < mean_abs_err,
                                    scaling_factor_value,
                                    1.0)
            amplitude_loss = tf.sqrt(tf.reduce_mean(scaling_mask * tf.square(errors)) + 1e-8)

            # ════════════════════════════════════════════════════════════════
            # 4. Pénalité d’écart-type (calibration de volatilité)
            # ════════════════════════════════════════════════════════════════
            pred_std = tf.math.reduce_std(y_pred)
            true_std = tf.math.reduce_std(y_true)
            std_penalty = tf.square(pred_std / (true_std + 1e-8) - 1.0)

            # ════════════════════════════════════════════════════════════════
            # 5. Bloc secondaire : mélange amplitude / trend (α)
            # ════════════════════════════════════════════════════════════════
            secondary_loss = (1.0 - alpha_val) * amplitude_loss + alpha_val * trend_loss

            # facteur de pénalisation supplémentaire si σ_pred ≠ σ_true
            amp_factor = tf.abs(pred_std - true_std) / (true_std + 1e-8)
            amplitude_loss_scaled = lambda_scale_val * amplitude_loss * amp_factor

            # ════════════════════════════════════════════════════════════════
            # 6. γ dynamique (option) : +0.3 lorsque la corrélation s’améliore
            # ════════════════════════════════════════════════════════════════
            gamma_dyn = tf.clip_by_value(gamma_val + 0.3 * tf.clip_by_value(r, 0.0, 1.0),
                                        0.0, 0.95)

            # ════════════════════════════════════════════════════════════════
            # 7. Pénalité de signe
            # ════════════════════════════════════════════════════════════════
            sign_true = tf.sign(y_true)
            sign_pred = tf.sign(y_pred)

            # 1 si erreur de signe, 0 si OK
            sign_error = tf.cast(tf.not_equal(sign_true, sign_pred), tf.float32)

            # pondéré par l’amplitude de y_true, éventuellement
            sign_weight = tf.abs(y_true) / (tf.reduce_max(tf.abs(y_true)) + 1e-8)

            sign_loss = tf.reduce_mean(sign_error * sign_weight)

            # ════════════════════════════════════════════════════════════════
            # 8. Combinaison finale
            # ════════════════════════════════════════════════════════════════
            final = (
                gamma_dyn * trend_loss +
                (1.0 - gamma_dyn) * secondary_loss +
                lambda_std_val * std_penalty +
                w_pearson * pearson_soft_loss         # ← facultatif (w_pearson=0 par défaut)
            )
            final += lambda_sign_val * sign_loss
            return final

        return loss





    def amplitude_directional_reversal_loss(self, alpha=0.6, beta=0.2, trend_lag=1, reversal_window=5, threshold=1e-3):
        """
        Loss combinant RMSE, tendance directionnelle, et détection des retournements.

        Args:
            alpha: poids de la tendance
            beta: poids du retournement
            trend_lag: pas de temps pour la tendance locale
            reversal_window: taille de la fenêtre pour détection de retournement
            threshold: seuil minimum pour considérer un changement de direction

        Returns:
            Fonction de perte
        """
        def loss(y_true, y_pred):
            # 1️⃣ RMSE
            amplitude_loss = tf.math.sqrt(tf.reduce_mean(tf.square(y_true - y_pred)) + 1e-8)

            # 2️⃣ Tendance directionnelle locale
            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]
            trend_match = tf.cast(tf.equal(tf.sign(delta_true), tf.sign(delta_pred)), tf.float32)
            trend_loss = 1.0 - tf.reduce_mean(trend_match)

            # 3️⃣ Retournements
            def detect_reversals(series):
                frames = tf.signal.frame(series, frame_length=reversal_window, frame_step=1, pad_end=False)
                before = frames[:-reversal_window]
                after = frames[reversal_window:]

                mean_before = tf.reduce_mean(before, axis=1)
                mean_after = tf.reduce_mean(after, axis=1)

                direction_change = tf.not_equal(tf.sign(mean_before), tf.sign(mean_after))
                significant = tf.abs(mean_before - mean_after) > threshold
                return tf.logical_and(direction_change, significant)

            # Assure alignement des longueurs
            min_len = tf.minimum(tf.shape(y_true)[0], tf.shape(y_pred)[0])
            y_true_crop = y_true[:min_len]
            y_pred_crop = y_pred[:min_len]

            true_reversals = detect_reversals(y_true_crop)
            pred_reversals = detect_reversals(y_pred_crop)

            match = tf.cast(tf.equal(true_reversals, pred_reversals), tf.float32)
            reversal_loss = 1.0 - tf.reduce_mean(match)

            # 🔚 Combinaison finale
            return (1 - alpha - beta) * amplitude_loss + alpha * trend_loss + beta * reversal_loss

        return loss




    def combined_directional_loss(self, delta=0.01, alpha=0.7):
        """
        Loss personnalisée combinant Huber/MSE + pénalisation du mauvais signe.

        Args:
            delta (float): seuil de la Huber loss
            alpha (float): poids du terme de direction (entre 0 et 1)

        Returns:
            Callable TensorFlow loss
        """
        def loss(y_true, y_pred):
            # Huber loss (robuste aux outliers)
            huber = tf.keras.losses.huber(y_true, y_pred, delta=delta)

            # Directional penalty : 1 si mauvais signe, 0 sinon
            wrong_sign = tf.cast(tf.not_equal(tf.sign(y_true), tf.sign(y_pred)), tf.float32)
            direction_penalty = tf.reduce_mean(wrong_sign)

            # Perte combinée
            return (1 - alpha) * huber + alpha * direction_penalty
        return loss


    '''
    def simple_correlation_loss(self, lambda_corr=0.1, lambda_sigma=0.01, lambda_sign=0.1):

        def loss_fn(y_true, y_pred):
            # 1) MSE (erreur d'amplitude)
            mse = tf.reduce_mean(tf.square(y_true - y_pred))

            # 2) Corrélation
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(tf.reduce_sum(y_t**2, axis=0) * tf.reduce_sum(y_p**2, axis=0)) + 1e-8
            corr = cov / denom
            corr_loss = 1 - tf.reduce_mean(corr)

            # 3) Pénalité de σ (écart-type)
            std_true = tf.math.reduce_std(y_true, axis=0) + 1e-8
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            sigma_penalty = tf.reduce_mean((std_pred - std_true)**2)  # Pénalité réduite

            # 4) Pénalité de signe (direction)
            sign_true = tf.sign(y_true[1:] - y_true[:-1])
            sign_pred = tf.sign(y_pred[1:] - y_pred[:-1])
            sign_penalty = tf.reduce_mean(tf.cast(sign_true != sign_pred, tf.float32))

            # Total loss
            return mse + lambda_corr * corr_loss + lambda_sigma * sigma_penalty + lambda_sign * sign_penalty

        return loss_fn
    '''

    '''
    #Version qui marchotte
    def simple_correlation_loss(self):

        def loss_fn(y_true, y_pred):
            # 1) MSE (erreur d'amplitude)
            mse = tf.reduce_mean(tf.square(y_true - y_pred))

            # 2) Corrélation
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(tf.reduce_sum(y_t**2, axis=0) * tf.reduce_sum(y_p**2, axis=0)) + 1e-8
            corr = cov / denom
            corr_loss = 1 - tf.reduce_mean(corr)

            # 3) Pénalité de σ (écart-type)
            std_true = tf.math.reduce_std(y_true, axis=0) + 1e-8
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            sigma_penalty = tf.reduce_mean((std_pred - std_true)**2)

            # 4) Pénalité de signe (direction)
            sign_true = tf.sign(y_true[1:] - y_true[:-1])
            sign_pred = tf.sign(y_pred[1:] - y_pred[:-1])
            sign_penalty = tf.reduce_mean(tf.cast(sign_true != sign_pred, tf.float32))

            # ✅ Calcul automatique des poids (inverses normalisés)
            epsilon = 1e-8
            inverse_mse = 1 / (mse + epsilon)
            inverse_corr = 1 / (corr_loss + epsilon)
            inverse_sigma = 1 / (sigma_penalty + epsilon)
            inverse_sign = 1 / (sign_penalty + epsilon)

            # ✅ Équilibrage des poids pour éviter les extrêmes
            total_inverse = (
                inverse_mse +
                inverse_corr * 2.0 +  # Corrélation renforcée
                inverse_sigma * 0.5 +  # Pénalité de σ réduite
                inverse_sign * 1.5     # Direction légèrement renforcée
            )

            weight_mse = (inverse_mse / total_inverse) * 0.4  # MSE dominant
            weight_corr = (inverse_corr / total_inverse) * 0.3  # Corr renforcé
            weight_sigma = (inverse_sigma / total_inverse) * 0.15  # Sigma faible
            weight_sign = (inverse_sign / total_inverse) * 0.15  # Direction ajustée

            # ✅ Total loss avec poids équilibrés
            total_loss = (
                weight_mse * mse +
                weight_corr * corr_loss +
                weight_sigma * sigma_penalty +
                weight_sign * sign_penalty
            )

            return total_loss

        return loss_fn
    '''

    '''
    # La dernière me donnait quelque chose de pas trop mal
    def simple_correlation_loss(self):
        def loss_fn(y_true, y_pred):
            # 1) MSE (erreur d'amplitude)
            mse = tf.reduce_mean(tf.square(y_true - y_pred))

            # 2) Corrélation (renforcée)
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(tf.reduce_sum(y_t**2, axis=0) * tf.reduce_sum(y_p**2, axis=0)) + 1e-8
            corr = cov / denom
            corr_loss = (1 - tf.reduce_mean(corr)) * 4.0  # ⚡ Plus de poids

            # 3) Pénalité de σ (écart-type ajustée)
            std_true = tf.math.reduce_std(y_true, axis=0) + 1e-8
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            sigma_penalty = tf.reduce_mean(tf.square(std_pred - std_true)) * 0.1

            # 4) Pénalité de signe (direction renforcée)
            sign_true = tf.sign(y_true[1:] - y_true[:-1])
            sign_pred = tf.sign(y_pred[1:] - y_pred[:-1])
            sign_penalty = tf.reduce_mean(tf.cast(sign_true != sign_pred, tf.float32)) * 2.0  # ⚡ Plus de poids

            # ✅ Calcul automatique des poids (inverses normalisés)
            epsilon = 1e-8
            inverse_mse = 1 / (mse + epsilon)
            inverse_corr = 1 / (corr_loss + epsilon)
            inverse_sigma = 1 / (sigma_penalty + epsilon)
            inverse_sign = 1 / (sign_penalty + epsilon)

            total_inverse = (
                inverse_mse * 0.25 +   # MSE moins dominant
                inverse_corr * 0.4 +   # Corr renforcée
                inverse_sigma * 0.05 +  # Sigma faible
                inverse_sign * 0.3     # Direction renforcée
            )

            weight_mse = inverse_mse / total_inverse
            weight_corr = inverse_corr / total_inverse
            weight_sigma = inverse_sigma / total_inverse
            weight_sign = inverse_sign / total_inverse

            # ✅ Total loss avec poids équilibrés
            total_loss = (
                weight_mse * mse +
                weight_corr * corr_loss +
                weight_sigma * sigma_penalty +
                weight_sign * sign_penalty
            )

            return total_loss

        return loss_fn
    '''


    def simple_correlation_loss(self):
        # Version qui marche
        def loss_fn(y_true, y_pred):
            # 1) Corrélation (très fortement pondérée)
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(tf.reduce_sum(y_t**2, axis=0) * tf.reduce_sum(y_p**2, axis=0)) + 1e-8
            corr = cov / denom
            corr_loss = (1 - tf.reduce_mean(corr)) * 10.0  # ✅ Corrélation dominante

            # 2) Contrainte d'Amplitude (écart-type adapté)
            std_true = tf.math.reduce_std(y_true, axis=0) + 1e-8
            std_pred = tf.math.reduce_std(y_pred, axis=0)

            # Ajustement dynamique de la pénalité en fonction de l'écart
            amplitude_penalty = tf.reduce_mean(tf.square(std_pred - std_true)) * 1.0

            # 3) Contrainte de MSE minimale (stabilité)
            mse = tf.reduce_mean(tf.square(y_true - y_pred)) * 0.05  # ⚡ Faible mais stabilisante

            # 4) Poids dynamiques (amplification de l'importance de l'amplitude si trop faible)
            epsilon = 1e-8
            inverse_corr = 1 / (corr_loss + epsilon)
            inverse_amplitude = 1 / (amplitude_penalty + epsilon)
            inverse_mse = 1 / (mse + epsilon)

            # Calcul du poids total dynamique
            total_inverse = (
                inverse_corr * 0.6 +   # Corrélation dominante
                inverse_amplitude * 0.35 +  # Amplitude cohérente
                inverse_mse * 0.05     # MSE très faible
            )

            weight_corr = inverse_corr / total_inverse
            weight_amplitude = inverse_amplitude / total_inverse
            weight_mse = inverse_mse / total_inverse

            # 5) Calcul de la loss finale
            total_loss = (
                weight_corr * corr_loss +
                weight_amplitude * amplitude_penalty +
                weight_mse * mse
            )

            return total_loss

        return loss_fn


    def custom_evaluation(self, trend_lag=1):
        def loss_fn(y_true, y_pred):

            metrics = self.evaluate_model_result(
                y_true, y_pred, trend_lag=trend_lag
            )
            score = 1 / metrics["combined_score"]
            return score
        return loss_fn



    def correlation_only_loss(self, eps=1e-8, beta_min=0.01, beta_max=1000.0):
        """
        Loss basée uniquement sur la corrélation entre y_true et y_pred.
        Renvoie 1 - corrélation moyenne (à minimiser).
        """
        def loss_fn(y_true, y_pred):
            # — 1) corrélation —
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(y_t), axis=0) *
                tf.reduce_sum(tf.square(y_p), axis=0)
            ) + eps
            corr_loss = 1.0 - tf.reduce_mean(cov / denom)

            # — 2) RMSE non normalisé —
            mse = tf.reduce_mean(tf.square(y_true - y_pred))
            rmse = tf.sqrt(mse + eps)

            # — 3) β dynamique (sans gradient) —
            beta_dyn = tf.stop_gradient(corr_loss / (rmse + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            # — 4) loss finale —
            return corr_loss + beta_dyn * rmse

        return loss_fn


    def correlation_mae_amplitude_loss(self, eps=1e-8,
                                        beta_min=0.01, beta_max=1000.0,
                                        gamma_min=0.01, gamma_max=1000.0):
        """
        Loss = corr_loss
            + beta_dyn  * rmse
            + gamma_dyn * amplitude_loss

        où :
        corr_loss      = 1 - mean(corrélation de Pearson)
        rmse           = sqrt(mean((y_true - y_pred)^2))
        amplitude_loss = mean((std(y_true) - std(y_pred))^2)

        beta_dyn  = clip(corr_loss / (rmse + eps),  beta_min, beta_max)
        gamma_dyn = clip(corr_loss / (amplitude_loss + eps), gamma_min, gamma_max)

        Args:
        eps         : petite constante de stabilité
        beta_min    : borne inférieure pour β_dyn
        beta_max    : borne supérieure pour β_dyn
        gamma_min   : borne inférieure pour γ_dyn
        gamma_max   : borne supérieure pour γ_dyn
        """
        def loss_fn(y_true, y_pred):
            # --- 1) Corrélation de Pearson ---
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(y_t), axis=0) *
                tf.reduce_sum(tf.square(y_p), axis=0)
            ) + eps
            corr = cov / denom
            corr_loss = 1.0 - tf.reduce_mean(corr)

            # --- 2) MAE non normalisé
            mae = tf.reduce_mean(tf.abs(y_true - y_pred))

            # --- 3) Amplitude (écart-type) ---
            std_true = tf.math.reduce_std(y_true, axis=0)
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            amplitude_loss = tf.reduce_mean(tf.square(std_true - std_pred))

            # --- 4) β_dyn & γ_dyn (stop_gradient pour ne pas rétropropager dans ces ratios) ---
            beta_dyn = tf.stop_gradient(corr_loss / (mae + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            gamma_dyn = tf.stop_gradient(corr_loss / (amplitude_loss + eps))
            gamma_dyn = tf.clip_by_value(gamma_dyn, gamma_min, gamma_max)

            #self.logger.send_log(f"In loss function", "info")


            # --- 5) Loss totale ---
            return corr_loss + beta_dyn * mae + gamma_dyn * amplitude_loss

        return loss_fn

    def corr_mae_amp_bias_loss(self, eps=1e-8,
                           beta_min=0.01, beta_max=1000.0,
                           gamma_min=0.01, gamma_max=1000.0,
                           theta_min=0.01, theta_max=1000.0):
        """
        Loss = corr_loss
            + beta_dyn  * MAE
            + gamma_dyn * amplitude_loss
            + theta_dyn * bias_loss

        où :
        corr_loss      = 1 − mean(PearsonCorr(y_true, y_pred))
        MAE            = mean(|y_true − y_pred|)
        amplitude_loss = mean((std(y_true) − std(y_pred))²)
        bias_loss      = mean((mean(y_true) − mean(y_pred))²)

        beta_dyn  = clip(corr_loss  / (MAE            + eps), beta_min,  beta_max)
        gamma_dyn = clip(corr_loss  / (amplitude_loss + eps), gamma_min, gamma_max)
        theta_dyn = clip(corr_loss  / (bias_loss      + eps), theta_min, theta_max)
        """
        def loss_fn(y_true, y_pred):
            # 1) corrélation de Pearson
            yt = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            yp = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(yt * yp, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(yt), axis=0) *
                tf.reduce_sum(tf.square(yp), axis=0)
            ) + eps
            corr = cov / denom
            corr_loss = 1.0 - tf.reduce_mean(corr)

            # 2) MAE
            mae = tf.reduce_mean(tf.abs(y_true - y_pred))

            # 3) Amplitude (écart-type)
            std_true = tf.math.reduce_std(y_true, axis=0)
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            amplitude_loss = tf.reduce_mean(tf.square(std_true - std_pred))

            # 4) Bias (décalage moyen)
            mean_true = tf.reduce_mean(y_true, axis=0)
            mean_pred = tf.reduce_mean(y_pred, axis=0)
            bias_loss = tf.reduce_mean(tf.square(mean_true - mean_pred))

            # 5) poids dynamiques
            beta_dyn = tf.stop_gradient(corr_loss / (mae + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            gamma_dyn = tf.stop_gradient(corr_loss / (amplitude_loss + eps))
            gamma_dyn = tf.clip_by_value(gamma_dyn, gamma_min, gamma_max)

            theta_dyn = tf.stop_gradient(corr_loss / (bias_loss + eps))
            theta_dyn = tf.clip_by_value(theta_dyn, theta_min, theta_max)


            #self.logger.send_log(f"✅ - Beta: {beta_dyn}, Gamma: {gamma_dyn}, Theta: {theta_dyn}", "info")

            # 6) loss totale
            return (
                corr_loss
            + beta_dyn      * mae
            + gamma_dyn     * amplitude_loss
            + theta_dyn     * bias_loss
            )

        return loss_fn


    def corr_mae_amp_deriv_loss(self, eps=1e-8,
                         beta_min=0.01,  beta_max=1000.0,
                         gamma_min=0.01, gamma_max=1000.0,
                         lambda_min=0.01, lambda_max=1000.0):
        """
        Loss = corr_loss
            + beta_dyn  * MAE
            + gamma_dyn * amplitude_loss
            + delta_dyn * deriv_loss

        - corr_loss      = 1 - mean(PearsonCorr)
        - MAE            = mean(|y_true - y_pred|)
        - amplitude_loss = mean((std(y_true)-std(y_pred))^2)
        - deriv_loss     = mean((Δy_true - Δy_pred)^2)

        beta_dyn  = clip(corr_loss / (MAE            + eps), beta_min,  beta_max)
        gamma_dyn = clip(corr_loss / (amplitude_loss + eps), gamma_min, gamma_max)
        delta_dyn = clip(corr_loss / (deriv_loss     + eps), delta_min, delta_max)
        """
        def loss_fn(y_true, y_pred):
            # --- 1) Corrélation de Pearson ---
            yt = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            yp = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(yt * yp, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(yt), axis=0) *
                tf.reduce_sum(tf.square(yp), axis=0)
            ) + eps
            corr = cov / denom
            corr_loss = 1.0 - tf.reduce_mean(corr)

            # --- 2) MAE ---
            mae = tf.reduce_mean(tf.abs(y_true - y_pred))

            # --- 3) Amplitude (écart-type) ---
            std_true = tf.math.reduce_std(y_true, axis=0)
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            amplitude_loss = tf.reduce_mean(tf.square(std_true - std_pred))

            # --- 4) R² loss ---
            y_mean = tf.reduce_mean(y_true, axis=0, keepdims=True)
            ssr = tf.reduce_sum(tf.square(y_true - y_pred))
            sst = tf.reduce_sum(tf.square(y_true - y_mean)) + eps
            r2_loss = ssr / sst  # = 1 − R²

            # --- 5) poids dynamiques (stop_gradient pour ne pas rétropropager) ---
            beta_dyn = tf.stop_gradient(corr_loss / (mae + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            gamma_dyn = tf.stop_gradient(corr_loss / (amplitude_loss + eps))
            gamma_dyn = tf.clip_by_value(gamma_dyn, gamma_min, gamma_max)

            lambda_dyn = tf.stop_gradient(corr_loss / (r2_loss + eps))
            lambda_dyn = tf.clip_by_value(lambda_dyn, lambda_min, lambda_max)

            # --- 6) Loss totale ---
            return (
                corr_loss
            + beta_dyn      * mae
            + gamma_dyn     * amplitude_loss
            + lambda_dyn    * r2_loss
            )

        return loss_fn




    def correlation_huber_loss(self, eps=1e-8,
                                beta_min=0.1,
                                beta_max=100.0,
                                delta=0.1):
        """
        Loss = alpha * (1 - corr_mean) + beta * huber
        • corr_mean = moyenne des corrélations de Pearson sur chaque dimension
        • huber = moyenne de la Huber loss avec seuil delta

        Arguments :
        alpha (float) – poids du terme de corrélation
        beta  (float) – poids du terme Huber
        delta (float) – seuil de transition MSE→MAE dans la Huber loss
        eps   (float) – epsilon pour stabilité numérique
        """
        def loss_fn(y_true, y_pred):
            # 1) Terme corrélation
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(y_t), axis=0) *
                tf.reduce_sum(tf.square(y_p), axis=0)
            ) + eps
            corr = cov / denom
            corr_loss = 1.0 - tf.reduce_mean(corr)

            # 2) Terme Huber
            error = y_true - y_pred
            abs_err = tf.abs(error)
            is_small = abs_err <= delta
            small_loss = 0.5 * tf.square(error)
            large_loss = delta * (abs_err - 0.5 * delta)
            huber = tf.where(is_small, small_loss, large_loss)
            huber_loss = tf.reduce_mean(huber)

            # 3) Calcul dynamique de β pour équilibrer corr_loss et huber_loss
            beta_dyn = tf.stop_gradient(corr_loss / (huber_loss + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            # 4) Loss finale
            return corr_loss + beta_dyn * huber_loss

        return loss_fn


    def balanced_reward_correlation_loss(self,
                                       corr_threshold=0.5,  # Abaissé de 0.7 à 0.5
                                       amp_threshold_low=0.7,  # Abaissé de 0.85 à 0.7
                                       amp_threshold_high=1.3,  # Augmenté de 1.15 à 1.3
                                       reward_factor=0.25,  # Augmenté de 0.15 à 0.25
                                       eps=1e-8,
                                       beta_min=0.01,
                                       beta_max=1000.0,
                                       gamma_min=0.01,
                                       gamma_max=1000.0):
        """
        Loss avec système de récompense équilibrée basée sur correlation_mae_amplitude_loss.
        Récompense SEULEMENT quand BOTH corrélation ET amplitude sont dans les bonnes plages.

        Principe :
        - Base correlation_mae_amplitude_loss comme fondation (votre meilleure loss)
        - Récompense progressive quand corrélation > seuil ET amplitude correcte
        - Pénalité supplémentaire si un seul critère est bon (évite les compromis)
        - Utilise les poids dynamiques β_dyn et γ_dyn de votre loss originale

        Args:
            corr_threshold: Seuil minimum de corrélation pour récompense (défaut: 0.7)
            amp_threshold_low: Ratio amplitude minimum acceptable (défaut: 0.85)
            amp_threshold_high: Ratio amplitude maximum acceptable (défaut: 1.15)
            reward_factor: Facteur de récompense maximum (défaut: 0.15)
            eps: Epsilon pour stabilité numérique
            beta_min/max: Bornes pour β_dyn (poids MAE)
            gamma_min/max: Bornes pour γ_dyn (poids amplitude)
        """
        def loss_fn(y_true, y_pred):
            # ═══════════════════════════════════════════════════════════════
            # 1. Base correlation_mae_amplitude_loss (votre meilleure loss)
            # ═══════════════════════════════════════════════════════════════
            # --- 1) Corrélation de Pearson ---
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(y_t), axis=0) *
                tf.reduce_sum(tf.square(y_p), axis=0)
            ) + eps
            corr = cov / denom
            corr_loss = 1.0 - tf.reduce_mean(corr)
            correlation = tf.reduce_mean(corr)  # Pour les conditions de récompense

            # --- 2) MAE ---
            mae = tf.reduce_mean(tf.abs(y_true - y_pred))

            # --- 3) Amplitude (écart-type) ---
            std_true = tf.math.reduce_std(y_true, axis=0)
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            amplitude_loss = tf.reduce_mean(tf.square(std_true - std_pred))
            amplitude_ratio = std_pred / (std_true + eps)  # Pour les conditions de récompense

            # --- 4) β_dyn & γ_dyn (poids dynamiques comme dans votre loss originale) ---
            beta_dyn = tf.stop_gradient(corr_loss / (mae + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            gamma_dyn = tf.stop_gradient(corr_loss / (amplitude_loss + eps))
            gamma_dyn = tf.clip_by_value(gamma_dyn, gamma_min, gamma_max)

            # --- 5) Loss de base (votre correlation_mae_amplitude originale) ---
            base_loss = corr_loss + beta_dyn * mae + gamma_dyn * amplitude_loss

            # ═══════════════════════════════════════════════════════════════
            # 2. Système de récompense équilibrée (ajout au-dessus de votre base)
            # ═══════════════════════════════════════════════════════════════
            # Conditions de qualité
            corr_good = tf.greater(correlation, corr_threshold)
            amp_good = tf.logical_and(
                tf.greater(amplitude_ratio, amp_threshold_low),
                tf.less(amplitude_ratio, amp_threshold_high)
            )

            # Les DEUX critères doivent être satisfaits pour la récompense
            both_good = tf.logical_and(corr_good, amp_good)

            # Récompense basée sur la qualité de la corrélation
            corr_quality = tf.clip_by_value(
                (correlation - corr_threshold) / (1.0 - corr_threshold),
                0.0, 1.0
            )

            # Récompense basée sur la précision de l'amplitude
            amp_center = (amp_threshold_low + amp_threshold_high) / 2.0
            amp_range = amp_threshold_high - amp_threshold_low
            amp_quality = tf.clip_by_value(
                1.0 - tf.abs(amplitude_ratio - amp_center) / (amp_range / 2.0),
                0.0, 1.0
            )

            # Récompense combinée (multiplicative pour encourager les deux)
            combined_quality = corr_quality * amp_quality

            # Récompense finale (seulement si both_good)
            reward = tf.where(both_good,
                            combined_quality * reward_factor * base_loss,
                            tf.constant(0.0, dtype=tf.float32))

            # ═══════════════════════════════════════════════════════════════
            # 3. Pénalités pour déséquilibres
            # ═══════════════════════════════════════════════════════════════
            # Pénalité si seulement un critère est bon (évite les compromis)
            only_corr_good = tf.logical_and(corr_good, tf.logical_not(amp_good))
            only_amp_good = tf.logical_and(amp_good, tf.logical_not(corr_good))

            imbalance_penalty = tf.where(
                tf.logical_or(only_corr_good, only_amp_good),
                base_loss * 0.1,  # 10% de pénalité supplémentaire
                tf.constant(0.0, dtype=tf.float32)
            )

            # ═══════════════════════════════════════════════════════════════
            # 4. Loss finale
            # ═══════════════════════════════════════════════════════════════
            final_loss = base_loss - reward + imbalance_penalty

            # Assurer que la loss reste positive (minimum 10% de la loss de base)
            final_loss = tf.maximum(final_loss, base_loss * 0.1)

            return final_loss

        return loss_fn



    def anti_suppression_reward_loss(self,
                                   corr_threshold=0.5,
                                   amp_threshold_low=0.6,
                                   amp_threshold_high=1.4,
                                   reward_factor=0.3,
                                   suppression_penalty=2.0,
                                   eps=1e-8,
                                   beta_min=0.01,
                                   beta_max=1000.0,
                                   gamma_min=0.01,
                                   gamma_max=1000.0):
        """
        Version anti-suppression de balanced_reward_correlation_loss.
        Pénalise TRÈS fortement la suppression d'amplitude.

        Args:
            suppression_penalty: Multiplicateur de pénalité pour suppression (défaut: 2.0)
            Autres paramètres: Plus permissifs que la version originale
        """
        def loss_fn(y_true, y_pred):
            # ═══════════════════════════════════════════════════════════════
            # 1. Base correlation_mae_amplitude_loss
            # ═══════════════════════════════════════════════════════════════
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(
                tf.reduce_sum(tf.square(y_t), axis=0) *
                tf.reduce_sum(tf.square(y_p), axis=0)
            ) + eps
            corr = cov / denom
            corr_loss = 1.0 - tf.reduce_mean(corr)
            correlation = tf.reduce_mean(corr)

            mae = tf.reduce_mean(tf.abs(y_true - y_pred))

            std_true = tf.math.reduce_std(y_true, axis=0)
            std_pred = tf.math.reduce_std(y_pred, axis=0)
            amplitude_loss = tf.reduce_mean(tf.square(std_true - std_pred))
            amplitude_ratio = std_pred / (std_true + eps)

            beta_dyn = tf.stop_gradient(corr_loss / (mae + eps))
            beta_dyn = tf.clip_by_value(beta_dyn, beta_min, beta_max)

            gamma_dyn = tf.stop_gradient(corr_loss / (amplitude_loss + eps))
            gamma_dyn = tf.clip_by_value(gamma_dyn, gamma_min, gamma_max)

            base_loss = corr_loss + beta_dyn * mae + gamma_dyn * amplitude_loss

            # ═══════════════════════════════════════════════════════════════
            # 2. Pénalité Anti-Suppression AGRESSIVE
            # ═══════════════════════════════════════════════════════════════
            # Pénalité exponentielle pour suppression sévère
            suppression_factor = tf.where(
                amplitude_ratio < 0.8,  # Si amplitude < 80% de l'original
                tf.pow(0.8 / (amplitude_ratio + 0.1), suppression_penalty),  # Pénalité exponentielle
                tf.constant(1.0, dtype=tf.float32)  # Pas de pénalité si amplitude OK
            )

            # Appliquer la pénalité à la loss de base
            penalized_base_loss = base_loss * suppression_factor

            # ═══════════════════════════════════════════════════════════════
            # 3. Système de récompense (plus permissif)
            # ═══════════════════════════════════════════════════════════════
            corr_good = tf.greater(correlation, corr_threshold)
            amp_good = tf.logical_and(
                tf.greater(amplitude_ratio, amp_threshold_low),
                tf.less(amplitude_ratio, amp_threshold_high)
            )

            both_good = tf.logical_and(corr_good, amp_good)

            # Récompense progressive
            corr_quality = tf.clip_by_value(
                (correlation - corr_threshold) / (1.0 - corr_threshold),
                0.0, 1.0
            )

            amp_center = (amp_threshold_low + amp_threshold_high) / 2.0
            amp_range = amp_threshold_high - amp_threshold_low
            amp_quality = tf.clip_by_value(
                1.0 - tf.abs(amplitude_ratio - amp_center) / (amp_range / 2.0),
                0.0, 1.0
            )

            combined_quality = corr_quality * amp_quality

            reward = tf.where(both_good,
                            combined_quality * reward_factor * penalized_base_loss,
                            tf.constant(0.0, dtype=tf.float32))

            # ═══════════════════════════════════════════════════════════════
            # 4. Loss finale
            # ═══════════════════════════════════════════════════════════════
            final_loss = penalized_base_loss - reward

            # Assurer que la loss reste positive
            final_loss = tf.maximum(final_loss, base_loss * 0.1)

            return final_loss

        return loss_fn


    def aggressive_correlation_volatility_loss(self, horizon_hours=3, trend_lag=1):
        """
        Aggressive loss function that heavily penalizes volatility suppression
        and prioritizes correlation improvement.

        Key improvements:
        1. Exponential penalty for volatility suppression
        2. Higher weight on correlation component
        3. Explicit trend direction component
        4. Horizon-specific adaptations

        Args:
            horizon_hours: Prediction horizon in hours
            trend_lag: Number of steps to use for trend calculation
        """
        def loss_fn(y_true, y_pred):
            # ════════════════════════════════════════════════════════════════
            # 1. Correlation Component (heavily weighted)
            # ════════════════════════════════════════════════════════════════
            # Center the data
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)

            # Calculate Pearson correlation
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(tf.reduce_sum(tf.square(y_t), axis=0) *
                            tf.reduce_sum(tf.square(y_p), axis=0) + 1e-8)
            corr = cov / denom

            # Aggressive correlation loss with exponential penalty for negative correlation
            corr_factor = tf.where(
                corr < 0,
                3.0,  # Triple penalty for negative correlation
                1.0
            )
            corr_loss = (1.0 - tf.reduce_mean(corr)) * 12.0 * corr_factor

            # ════════════════════════════════════════════════════════════════
            # 2. Aggressive Volatility Matching
            # ════════════════════════════════════════════════════════════════
            std_true = tf.math.reduce_std(y_true, axis=0) + 1e-8
            std_pred = tf.math.reduce_std(y_pred, axis=0)

            # Volatility ratio (pred/true)
            vol_ratio = std_pred / std_true

            # Exponential penalty for volatility suppression
            # This creates a much steeper penalty curve as volatility decreases
            suppression_exponent = 3.0 + (horizon_hours / 3.0)  # Higher exponent for longer horizons

            vol_penalty = tf.where(
                vol_ratio < 0.9,  # Significant suppression
                tf.pow(1.0 / (vol_ratio + 0.1), suppression_exponent),  # Exponential penalty
                tf.where(
                    vol_ratio > 1.1,  # Over-prediction
                    tf.pow(vol_ratio, 2.0),  # Quadratic penalty for over-prediction
                    1.0  # Minimal penalty in the "good" range
                )
            )

            # Base volatility loss
            vol_base_loss = tf.square(vol_ratio - 1.0) * 8.0
            volatility_loss = vol_base_loss * vol_penalty

            # ════════════════════════════════════════════════════════════════
            # 3. Explicit Trend Direction Component
            # ════════════════════════════════════════════════════════════════
            # Calculate trend directions
            seq_length = tf.shape(y_true)[0]

            def calculate_trend_direction_loss():
                # Calculate changes over multiple timeframes for robustness
                trend_losses = []

                for lag in [1, trend_lag, max(1, trend_lag * 2)]:
                    # Use TensorFlow conditional instead of Python if
                    def compute_trend_for_lag():
                        delta_true = y_true[lag:] - y_true[:-lag]
                        delta_pred = y_pred[lag:] - y_pred[:-lag]

                        # Direction match
                        true_direction = tf.sign(delta_true)
                        pred_direction = tf.sign(delta_pred)
                        direction_match = tf.cast(tf.equal(true_direction, pred_direction), tf.float32)

                        # Weight by magnitude of true change
                        delta_magnitude = tf.abs(delta_true)
                        mean_delta = tf.reduce_mean(delta_magnitude) + 1e-8
                        magnitude_weight = delta_magnitude / mean_delta

                        # Apply magnitude weighting
                        weighted_direction_match = direction_match * magnitude_weight
                        direction_accuracy = tf.reduce_mean(weighted_direction_match)

                        # Penalize incorrect directions
                        return (1.0 - direction_accuracy) * 6.0

                    def skip_lag():
                        return tf.constant(0.0, dtype=tf.float32)

                    # Use tf.cond instead of Python if
                    trend_loss = tf.cond(
                        seq_length > lag,
                        compute_trend_for_lag,
                        skip_lag
                    )

                    # Only add non-zero losses
                    trend_losses.append(trend_loss)

                # Average across timeframes, filtering out zero values
                if trend_losses:
                    stacked_losses = tf.stack(trend_losses)
                    # Only average non-zero losses
                    non_zero_mask = tf.greater(stacked_losses, 0.0)
                    valid_losses = tf.boolean_mask(stacked_losses, non_zero_mask)
                    return tf.cond(
                        tf.size(valid_losses) > 0,
                        lambda: tf.reduce_mean(valid_losses),
                        lambda: tf.constant(0.0, dtype=tf.float32)
                    )
                else:
                    return tf.constant(0.0, dtype=tf.float32)

            trend_direction_loss = tf.cond(
                seq_length > trend_lag,
                calculate_trend_direction_loss,
                lambda: tf.constant(0.0, dtype=tf.float32)
            )

            # ════════════════════════════════════════════════════════════════
            # 4. Horizon-Specific Weighting
            # ════════════════════════════════════════════════════════════════
            # Calculate horizon factor (0.0 for short horizons, approaches 1.0 for long horizons)
            horizon_factor = tf.minimum(horizon_hours / 12.0, 1.0)

            # Base weights adjusted by horizon
            corr_weight = 0.50 - (0.10 * horizon_factor)        # Slight decrease with horizon
            volatility_weight = 0.30 + (0.20 * horizon_factor)  # Significant increase with horizon
            direction_weight = 0.20 - (0.10 * horizon_factor)   # Slight decrease with horizon

            # ════════════════════════════════════════════════════════════════
            # 5. Dynamic Component Balancing
            # ════════════════════════════════════════════════════════════════
            epsilon = 1e-8

            # Inverse values (smaller loss = larger weight)
            inverse_corr = 1.0 / (corr_loss + epsilon)
            inverse_volatility = 1.0 / (volatility_loss + epsilon)
            inverse_direction = 1.0 / (trend_direction_loss + epsilon)

            # Calculate total inverse with importance factors
            total_inverse = (
                inverse_corr * corr_weight +
                inverse_volatility * volatility_weight +
                inverse_direction * direction_weight
            )

            # Final dynamic weights
            weight_corr = inverse_corr / total_inverse
            weight_volatility = inverse_volatility / total_inverse
            weight_direction = inverse_direction / total_inverse

            # ════════════════════════════════════════════════════════════════
            # 6. Final Combined Loss
            # ════════════════════════════════════════════════════════════════
            total_loss = (
                weight_corr * corr_loss +
                weight_volatility * volatility_loss +
                weight_direction * trend_direction_loss
            )

            return total_loss

        return loss_fn

    def horizon_adaptive_correlation_loss(self, horizon_hours=3, trend_lag=1):
        """
        Enhanced correlation-focused loss function with horizon-specific adaptations
        to address volatility suppression and maintain directional accuracy.

        Key improvements:
        1. Horizon-specific weighting of components
        2. Anti-suppression penalty for volatility
        3. Adaptive trend component based on horizon length
        4. Balanced correlation and directional components

        Args:
            horizon_hours: Prediction horizon in hours (affects weighting)
            trend_lag: Number of steps to use for trend calculation

        Returns:
            Callable loss function for model compilation
        """
        def loss_fn(y_true, y_pred):
            # ════════════════════════════════════════════════════════════════
            # 1. Correlation Component (pattern matching)
            # ════════════════════════════════════════════════════════════════
            # Center the data
            y_t = y_true - tf.reduce_mean(y_true, axis=0, keepdims=True)
            y_p = y_pred - tf.reduce_mean(y_pred, axis=0, keepdims=True)

            # Calculate Pearson correlation
            cov = tf.reduce_sum(y_t * y_p, axis=0)
            denom = tf.sqrt(tf.reduce_sum(tf.square(y_t), axis=0) *
                            tf.reduce_sum(tf.square(y_p), axis=0) + 1e-8)
            corr = cov / denom
            corr_loss = (1.0 - tf.reduce_mean(corr)) * 8.0

            # ════════════════════════════════════════════════════════════════
            # 2. Volatility Component with Anti-Suppression
            # ════════════════════════════════════════════════════════════════
            # Standard deviation matching with anti-suppression penalty
            std_true = tf.math.reduce_std(y_true, axis=0) + 1e-8
            std_pred = tf.math.reduce_std(y_pred, axis=0)

            # Volatility ratio (pred/true)
            vol_ratio = std_pred / std_true

            # Asymmetric penalty that heavily penalizes volatility suppression
            # Stronger penalty for longer horizons which showed more suppression
            suppression_factor = 2.0 + (horizon_hours / 3.0)  # Scales with horizon
            amplification_factor = 1.0  # Fixed penalty for over-prediction

            # Apply asymmetric penalty
            vol_penalty_factor = tf.where(
                vol_ratio < 0.9,  # Significant suppression
                suppression_factor * (1.0 / (vol_ratio + 0.1)),  # Inverse relationship for suppression
                tf.where(
                    vol_ratio > 1.1,  # Significant amplification
                    amplification_factor * vol_ratio,  # Linear penalty for amplification
                    1.0  # Minimal penalty in the "good" range (0.9-1.1)
                )
            )

            # Final volatility penalty with horizon-specific scaling
            vol_penalty_base = tf.square(vol_ratio - 1.0) * 5.0
            volatility_loss = vol_penalty_base * vol_penalty_factor

            # ════════════════════════════════════════════════════════════════
            # 3. Range Preservation Component
            # ════════════════════════════════════════════════════════════════
            # Ensure the prediction range (min to max) is similar to the true range
            range_true = tf.reduce_max(y_true, axis=0) - tf.reduce_min(y_true, axis=0) + 1e-8
            range_pred = tf.reduce_max(y_pred, axis=0) - tf.reduce_min(y_pred, axis=0)

            # Range ratio with similar asymmetric penalty
            range_ratio = range_pred / range_true
            range_penalty_factor = tf.where(
                range_ratio < 0.8,  # Significant range suppression
                suppression_factor,
                tf.where(
                    range_ratio > 1.2,  # Significant range amplification
                    amplification_factor,
                    1.0  # Good range
                )
            )

            range_loss = tf.square(range_ratio - 1.0) * 3.0 * range_penalty_factor

            # ════════════════════════════════════════════════════════════════
            # 4. Direction Component (sign prediction)
            # ════════════════════════════════════════════════════════════════
            # Sign matching (positive vs negative)
            sign_true = tf.sign(y_true)
            sign_pred = tf.sign(y_pred)
            sign_match = tf.cast(tf.equal(sign_true, sign_pred), tf.float32)

            # Weight by magnitude (more important to get direction right for large moves)
            magnitude = tf.abs(y_true)
            mean_magnitude = tf.reduce_mean(magnitude) + 1e-8
            magnitude_weight = magnitude / mean_magnitude

            # Apply magnitude weighting to sign matching
            weighted_sign_match = sign_match * magnitude_weight
            direction_loss = (1.0 - tf.reduce_mean(weighted_sign_match)) * 4.0

            # ════════════════════════════════════════════════════════════════
            # 5. Trend Component (sequential movement)
            # ════════════════════════════════════════════════════════════════
            # Skip if sequence is too short
            seq_length = tf.shape(y_true)[0]

            def calculate_trend_loss():
                # Calculate changes over the trend lag
                delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
                delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]

                # Direction of movement
                trend_sign_true = tf.sign(delta_true)
                trend_sign_pred = tf.sign(delta_pred)
                trend_match = tf.cast(tf.equal(trend_sign_true, trend_sign_pred), tf.float32)

                # Weight by magnitude of true change
                delta_magnitude = tf.abs(delta_true)
                mean_delta = tf.reduce_mean(delta_magnitude) + 1e-8
                delta_weight = delta_magnitude / mean_delta

                # Apply magnitude weighting to trend matching
                weighted_trend_match = trend_match * delta_weight

                # Horizon-specific trend importance
                # For longer horizons, trend becomes more important than point-by-point direction
                trend_importance = 3.0 + (horizon_hours / 3.0)

                return (1.0 - tf.reduce_mean(weighted_trend_match)) * trend_importance

            # Only calculate trend if sequence is long enough
            trend_loss = tf.cond(
                seq_length > trend_lag,
                calculate_trend_loss,
                lambda: tf.constant(0.0, dtype=tf.float32)
            )

            # ════════════════════════════════════════════════════════════════
            # 6. MSE Stabilization Component (small regularization)
            # ════════════════════════════════════════════════════════════════
            # Small MSE term for stability, reduced for longer horizons
            mse_factor = 0.05 / (1.0 + (horizon_hours / 10.0))  # Reduces as horizon increases
            mse = tf.reduce_mean(tf.square(y_true - y_pred)) * mse_factor

            # ════════════════════════════════════════════════════════════════
            # 7. Horizon-Adaptive Weighting System
            # ════════════════════════════════════════════════════════════════
            # Base importance weights adjusted by horizon
            # For longer horizons: increase volatility and trend importance
            # For shorter horizons: prioritize correlation and direction

            # Calculate horizon factor (0.0 for short horizons, approaches 1.0 for long horizons)
            horizon_factor = tf.minimum(horizon_hours / 12.0, 1.0)

            # Adjust weights based on horizon
            corr_importance = 0.45 - (0.15 * horizon_factor)        # Decreases with horizon
            volatility_importance = 0.20 + (0.20 * horizon_factor)  # Increases with horizon
            range_importance = 0.10 + (0.05 * horizon_factor)       # Slight increase with horizon
            direction_importance = 0.15 - (0.05 * horizon_factor)   # Slight decrease with horizon
            trend_importance = 0.10 + (0.10 * horizon_factor)       # Increases with horizon
            mse_importance = 0.05 - (0.03 * horizon_factor)         # Decreases with horizon

            # ════════════════════════════════════════════════════════════════
            # 8. Dynamic Component Balancing
            # ════════════════════════════════════════════════════════════════
            epsilon = 1e-8

            # Inverse values (smaller loss = larger weight)
            inverse_corr = 1.0 / (corr_loss + epsilon)
            inverse_volatility = 1.0 / (volatility_loss + epsilon)
            inverse_range = 1.0 / (range_loss + epsilon)
            inverse_direction = 1.0 / (direction_loss + epsilon)
            inverse_trend = 1.0 / (trend_loss + epsilon)
            inverse_mse = 1.0 / (mse + epsilon)

            # Calculate total inverse with importance factors
            total_inverse = (
                inverse_corr * corr_importance +
                inverse_volatility * volatility_importance +
                inverse_range * range_importance +
                inverse_direction * direction_importance +
                inverse_trend * trend_importance +
                inverse_mse * mse_importance
            )

            # Final dynamic weights
            weight_corr = inverse_corr / total_inverse
            weight_volatility = inverse_volatility / total_inverse
            weight_range = inverse_range / total_inverse
            weight_direction = inverse_direction / total_inverse
            weight_trend = inverse_trend / total_inverse
            weight_mse = inverse_mse / total_inverse

            # ════════════════════════════════════════════════════════════════
            # 9. Final Combined Loss
            # ════════════════════════════════════════════════════════════════
            total_loss = (
                weight_corr * corr_loss +
                weight_volatility * volatility_loss +
                weight_range * range_loss +
                weight_direction * direction_loss +
                weight_trend * trend_loss +
                weight_mse * mse
            )

            return total_loss

        return loss_fn

    def amplitude_constrained_mse_loss(self, volatility_penalty_factor=50.0, max_volatility_ratio=3.0):
        """
        MSE loss with strong amplitude/volatility constraints to prevent over-amplification.

        Args:
            volatility_penalty_factor: How strongly to penalize volatility mismatch
            max_volatility_ratio: Maximum allowed ratio of pred_std/true_std

        Returns:
            Callable loss function
        """
        def loss_fn(y_true, y_pred):
            # 1. Base MSE loss
            mse = tf.reduce_mean(tf.square(y_true - y_pred))

            # 2. Volatility constraint
            std_true = tf.math.reduce_std(y_true) + 1e-8
            std_pred = tf.math.reduce_std(y_pred)

            # Volatility ratio (pred/true)
            vol_ratio = std_pred / std_true

            # Strong penalty for over-amplification
            vol_penalty = tf.where(
                vol_ratio > max_volatility_ratio,
                tf.square(vol_ratio - max_volatility_ratio) * volatility_penalty_factor,
                tf.where(
                    vol_ratio > 1.2,  # Moderate over-amplification
                    tf.square(vol_ratio - 1.0) * (volatility_penalty_factor * 0.5),
                    tf.where(
                        vol_ratio < 0.8,  # Under-amplification
                        tf.square(1.0 - vol_ratio) * (volatility_penalty_factor * 0.3),
                        0.0  # Good range [0.8, 1.2]
                    )
                )
            )

            # 3. Range constraint (similar to volatility but for min-max range)
            range_true = tf.reduce_max(y_true) - tf.reduce_min(y_true) + 1e-8
            range_pred = tf.reduce_max(y_pred) - tf.reduce_min(y_pred)
            range_ratio = range_pred / range_true

            range_penalty = tf.where(
                range_ratio > max_volatility_ratio,
                tf.square(range_ratio - max_volatility_ratio) * (volatility_penalty_factor * 0.3),
                tf.where(
                    range_ratio < 0.5,
                    tf.square(0.5 - range_ratio) * (volatility_penalty_factor * 0.2),
                    0.0
                )
            )

            # 4. Combine losses
            total_loss = mse + vol_penalty + range_penalty

            return total_loss

        return loss_fn








    #####################################################################################################
    # COMPILATION MODEL FUNCTIONS
    #####################################################################################################

    def create_sequences_shuffled(self, X, y, n_steps=30, shuffle_blocks=False, seed=SEED):
        X_seq, y_seq = [], []
        for i in range(len(X) - n_steps):
            X_seq.append(X[i:i + n_steps])
            y_seq.append(y[i + n_steps])

        X_seq, y_seq = np.array(X_seq), np.array(y_seq)

        if shuffle_blocks:
            # Nouveau générateur stable et isolé
            rng = np.random.default_rng(seed)

            block_size = n_steps  # un bloc = une séquence
            n_blocks = len(X_seq) // block_size

            # ✅ Indices de blocs mélangés de manière déterministe
            block_indices = rng.permutation(n_blocks)

            X_shuffled, y_shuffled = [], []
            for idx in block_indices:
                start = idx * block_size
                end = start + block_size
                X_shuffled.extend(X_seq[start:end])
                y_shuffled.extend(y_seq[start:end])

            # Retourne des arrays bien formés
            return np.array(X_shuffled), np.array(y_shuffled)

        return X_seq, y_seq


    def _build_enhanced_cnn_block(self, x, init, **kwargs):
        conv_filters = kwargs.get("conv_filters", 64)
        kernel_size = kwargs.get("kernel_size", 3)
        pool_size = kwargs.get("pool_size", 2)
        use_batch_norm = kwargs.get("use_batch_norm", False)
        activation = kwargs.get("activation", "relu")

        # Multiple parallel convolutional paths (Inception-style)
        conv1 = Conv1D(conv_filters // 2, kernel_size=1, activation=activation,
                    padding='same', kernel_initializer=init)(x)

        conv3 = Conv1D(conv_filters // 2, kernel_size=3, activation=activation,
                    padding='same', kernel_initializer=init)(x)

        conv5 = Conv1D(conv_filters // 2, kernel_size=5, activation=activation,
                    padding='same', kernel_initializer=init)(x)

        # Concatenate different kernel sizes
        x = Concatenate()([conv1, conv3, conv5])

        if use_batch_norm:
            x = BatchNormalization()(x)

        # Always add residual connection with projection to avoid tensor shape comparison
        # Project input to match output dimension
        shortcut = Conv1D(int(conv_filters * 1.5), kernel_size=1,
                        padding='same', kernel_initializer=init)(x)
        x = Add()([x, shortcut])

        x = MaxPooling1D(pool_size=pool_size)(x)
        return x


    def _build_tcn_block(self, x, init, **kwargs):
        from tensorflow.keras.layers import Conv1D, Activation, SpatialDropout1D

        n_filters = kwargs.get("tcn_filters", 64)
        kernel_size = kwargs.get("tcn_kernel_size", 3)
        dilation_rates = [1, 2, 4, 8]

        for dilation_rate in dilation_rates:
            # Dilated causal convolution
            shortcut = x

            # First conv in residual block
            x = Conv1D(n_filters, kernel_size, padding='causal',
                    dilation_rate=dilation_rate, kernel_initializer=init)(x)
            x = BatchNormalization()(x)
            x = Activation('relu')(x)
            x = SpatialDropout1D(0.1)(x)

            # Second conv in residual block
            x = Conv1D(n_filters, kernel_size, padding='causal',
                    dilation_rate=dilation_rate, kernel_initializer=init)(x)
            x = BatchNormalization()(x)
            x = Activation('relu')(x)
            x = SpatialDropout1D(0.1)(x)

            # Always add residual connection with projection to avoid tensor shape comparison
            shortcut = Conv1D(n_filters, 1, padding='same', kernel_initializer=init)(shortcut)
            x = Add()([x, shortcut])

        return x


    def _build_enhanced_attention(self, x, **kwargs):
        num_heads = kwargs.get("mha_heads", 4)
        key_dim = kwargs.get("mha_key_dim", 32)
        dropout_rate = kwargs.get("mha_dropout", 0.1)

        # Self-attention branch
        norm_x = LayerNormalization(epsilon=1e-6)(x)
        self_attn = MultiHeadAttention(
            num_heads=num_heads,
            key_dim=key_dim,
            dropout=dropout_rate
        )(norm_x, norm_x)
        self_attn = Dropout(dropout_rate)(self_attn)

        # Add residual connection
        x1 = Add()([x, self_attn])
        x1 = LayerNormalization(epsilon=1e-6)(x1)

        # Feed-forward network (as in Transformer) with fixed dimensions
        ffn = Dense(256, activation='relu')(x1)  # Fixed size
        ffn = Dropout(dropout_rate)(ffn)
        ffn = Dense(128)(ffn)  # Fixed size

        # Add second residual connection
        x2 = Add()([x1, ffn])
        x2 = LayerNormalization(epsilon=1e-6)(x2)

        return x2


    def _build_feature_fusion(self, cnn_features, lstm_features, attention_features):
        # Always apply global pooling to avoid tensor shape comparisons
        cnn_features = GlobalAveragePooling1D()(cnn_features)
        lstm_features = GlobalAveragePooling1D()(lstm_features)
        attention_features = GlobalAveragePooling1D()(attention_features)

        # Concatenate all features
        combined = Concatenate()([cnn_features, lstm_features, attention_features])

        # Feature fusion with attention using fixed dimensions
        fusion_attn = Dense(128, activation='tanh')(combined)  # Fixed size
        fusion_weights = Dense(3, activation='softmax')(fusion_attn)

        # Apply weights to each feature type
        weighted_cnn = Multiply()([cnn_features, fusion_weights[:, 0:1]])
        weighted_lstm = Multiply()([lstm_features, fusion_weights[:, 1:2]])
        weighted_attn = Multiply()([attention_features, fusion_weights[:, 2:3]])

        # Final fusion
        fused = Add()([weighted_cnn, weighted_lstm, weighted_attn])

        return fused


    def attention_block(self, inputs, attention_units, activation, name_prefix, init):
        attention_dense = Dense(
            attention_units,
            activation=activation,
            kernel_initializer=init,
            bias_initializer='zeros',
            name=f"{name_prefix}_dense"
        )(inputs)

        attention_weights = Dense(
            1,
            activation='linear',
            kernel_initializer=init,
            bias_initializer='zeros',
            name=f"{name_prefix}_weights"
        )(attention_dense)

        attention_weights = Softmax(axis=1, name=f"{name_prefix}_softmax")(attention_weights)

        return Multiply(name=f"{name_prefix}_multiply")([inputs, attention_weights])


    def _apply_global_pooling_and_dropout(self, x, **kwargs):
        # Always apply global pooling to avoid tensor shape comparison
        x = GlobalAveragePooling1D(name="global_avg_pool_final")(x)
        if kwargs.get("use_dropout", True) and kwargs.get("dropout_rate", 0.2) > 0:
            x = Dropout(kwargs.get("dropout_rate", 0.2), seed=SEED, name="dropout_final")(x)
        return x

    def _build_output_layer(self, x, init, output_l2_reg):
        return Dense(
            1,
            activation=None,
            kernel_initializer=init,
            bias_initializer='zeros',
            kernel_regularizer=l2(output_l2_reg),
            name="output_final"
        )(x)

    def _get_loss_function(
            self,
            loss_type: str,
            delta: float,
            gamma_val: float,
            alpha_val: float,
            lambda_std_val: float,
            lambda_scale_val: float,
            lambda_sign_val: float,
            trend_lag: int,
            w_pearson: float,
            horizon_hour: int
        ):
        """
        Sélectionne et paramètre la fonction de perte.

        Paramètres clés
        ---------------
        delta : float
            Seuil Huber / amplitude.
        gamma_val : float
            Poids global « trend vs amplitude ».
        alpha_val : float
            Poids local dans les pertes mixtes.
        lambda_std_val : float
            Pénalité d’écart-type pour la calibration.
        lambda_scale_val : float
            Facteur de pondération de l’amplitude.
        trend_lag : int
            Lag utilisé pour calculer la tendance locale.
        """
        if loss_type == "huber":
            return tf.keras.losses.Huber(delta=delta)

        elif loss_type == "mse":
            return tf.keras.losses.MeanSquaredError()

        elif loss_type == "mae":
            return tf.keras.losses.MeanAbsoluteError()

        # ──────────────────────────────────────────────────────────────
        #  Losses directionnelles / mixtes
        # ──────────────────────────────────────────────────────────────
        elif loss_type == "directional_loss":
            # delta = seuil Huber, alpha_val = poids direction vs huber
            return self.combined_directional_loss(delta=delta, alpha=alpha_val)

        elif loss_type == "amplitude_directional":
            # (1-alpha_val)·amplitude  +  alpha_val·trend
            return self.amplitude_directional_loss(
                delta=delta,
                alpha=alpha_val,
                trend_lag=trend_lag
            )

        elif loss_type == "amplitude_directional_reversal":
            # alpha_val → poids trend, beta fixe à 0.2 (modifiable si besoin)
            return self.amplitude_directional_reversal_loss(
                alpha=alpha_val,
                beta=0.2,
                trend_lag=trend_lag,
                reversal_window=5,
                threshold=1e-3
            )

        # ──────────────────────────────────────────────────────────────
        #  Losses multi-objectifs (trend + amplitude + calibration)
        # ──────────────────────────────────────────────────────────────
        elif loss_type == "dual_objective":
            return self.dual_objective_loss(
                gamma_val=gamma_val,
                alpha_val=alpha_val,
                trend_lag=trend_lag,
                lambda_std_val=lambda_std_val,
                lambda_scale_val=lambda_scale_val
            )

        elif loss_type == "pearson_da_amplitude":
            return self.pearson_trend_amplitude_loss(
                gamma_val=gamma_val,
                alpha_val=alpha_val,
                trend_lag=trend_lag,
                lambda_std_val=lambda_std_val
            )

        elif loss_type == "adaptive_dual_objective":
            return self.adaptive_dual_objective_loss(
                gamma_val=gamma_val,
                alpha_val=alpha_val,
                trend_lag=trend_lag,
                lambda_std_val=lambda_std_val,
                lambda_scale_val=lambda_scale_val,
                lambda_sign_val=lambda_sign_val,
                w_pearson=w_pearson
            )

        elif loss_type == "simple_correlation":
            return self.simple_correlation_loss()
        elif loss_type == "enhanced_correlation":
            return self.horizon_adaptive_correlation_loss(horizon_hour, trend_lag)
        elif loss_type == 'aggressive_loss':
            return self.aggressive_correlation_volatility_loss(horizon_hour, trend_lag)
        elif loss_type == 'correlation_only':
            return self.correlation_only_loss()
        elif loss_type == 'custom_loss':
            return self.custom_evaluation(trend_lag)
        elif loss_type == 'correlation_huber':
            return self.correlation_huber_loss(delta=delta)
        elif loss_type == 'correlation_mae_amplitude':
            return self.correlation_mae_amplitude_loss()
        elif loss_type == 'corr_mae_amp_deriv':
            return self.corr_mae_amp_deriv_loss()
        elif loss_type == 'corr_mae_amp_bias':
            return self.corr_mae_amp_bias_loss()
        elif loss_type == 'balanced_reward':
            return self.balanced_reward_correlation_loss()
        elif loss_type == 'anti_suppression':
            return self.anti_suppression_reward_loss()
        else:
            raise ValueError(f"❌ Perte inconnue : {loss_type}")



    def attention_context_block_test(self, inputs, attention_units, activation, name_prefix, init):
        attention_dense = Dense(attention_units, activation=activation,
                                kernel_initializer=init, name=f"{name_prefix}_dense")(inputs)

        attention_scores = Dense(1, activation="linear", kernel_initializer=init,
                                name=f"{name_prefix}_scores")(attention_dense)

        attention_weights = Softmax(axis=1, name=f"{name_prefix}_softmax")(attention_scores)

        weighted = Multiply(name=f"{name_prefix}_weighted")([inputs, attention_weights])
        context_vector = Lambda(
            lambda t: tf.reduce_sum(t, axis=1),
            name=f"{name_prefix}_context_vector"
        )(weighted)

        return context_vector


    def _build_feature_extractor_test_lstm(self, inputs, init, **kwargs):
        use_conv = kwargs.get("use_conv", True)
        use_batch_norm = kwargs.get("use_batch_norm", False)
        activation = kwargs.get("activation", "relu")

        conv_filters = kwargs.get("conv_filters", 64)
        kernel_size = kwargs.get("kernel_size", 3)
        pool_size = kwargs.get("pool_size", 2)

        # LSTM config
        lstm_units_1 = kwargs.get("lstm_units_1", 64)
        lstm_units_2 = kwargs.get("lstm_units_2", 64)
        lstm_dropout = kwargs.get("lstm_dropout", 0.2)
        use_bidirectional = kwargs.get("use_bidirectional", False)

        # Attention config
        use_attention = kwargs.get("use_attention", True)
        mha_heads = kwargs.get("mha_heads", 4)
        mha_key_dim = kwargs.get("mha_key_dim", 32)
        mha_dropout = kwargs.get("mha_dropout", 0.1)

        x = inputs

        # 🧱 Bloc convolutionnel initial
        if use_conv:
            x = Conv1D(conv_filters, kernel_size=kernel_size, activation=activation,
                    padding='same', kernel_initializer=init, bias_initializer='zeros',
                    name="conv1d_initial_test")(x)
            if use_batch_norm:
                x = BatchNormalization(name="batch_norm_initial_test")(x)
        else:
            x = Dense(conv_filters, activation=activation,
                    kernel_initializer=init, bias_initializer='zeros',
                    name='dense_fallback_test')(x)

        x = MaxPooling1D(pool_size=pool_size, name="max_pool_initial_test")(x)


        # 🔁 1ère couche LSTM
        lstm_layer_1 = LSTM(
            lstm_units_1, return_sequences=True,
            kernel_initializer=init, recurrent_initializer=init,
            bias_initializer='zeros', dropout=lstm_dropout,
            seed=SEED,
            name="lstm_1_test"
        )
        if use_bidirectional:
            x = Bidirectional(lstm_layer_1, name="bidir_lstm_1_test")(x)
        else:
            x = lstm_layer_1(x)

        # 🔁 2ème couche LSTM
        lstm_layer_2 = LSTM(
            lstm_units_2, return_sequences=True if use_attention else False,
            kernel_initializer=init, recurrent_initializer=init,
            bias_initializer='zeros', dropout=lstm_dropout,
            seed=SEED,
            name="lstm_2_test"
        )
        if use_bidirectional:
            x = Bidirectional(lstm_layer_2, name="bidir_lstm_2_test")(x)
        else:
            x = lstm_layer_2(x)

        # 🧠 Attention
        if use_attention:
            x = self.multihead_attention_block_test(
                x,
                num_heads=mha_heads,
                key_dim=mha_key_dim,
                dropout_rate=mha_dropout,
                name_prefix="mha_test"
            )
        else:
            x = self.attention_block(
                x,
                attention_units=64,
                activation=activation,
                name_prefix="attention_simple",
                init=init)

        # 🌐 Pooling final
        if len(x.shape) == 3:
            x = GlobalAveragePooling1D(name="global_pool_test")(x)

        return x





    def build_cnn_attention_lstm_model_test(self, input_shape, **kwargs):
        init = tf.keras.initializers.GlorotUniform(seed=SEED)


        inputs = Input(shape=input_shape, dtype='float32', name="input_layer_test")
        x = self._build_feature_extractor_test_lstm(inputs, init, **kwargs)
        # Normalisation avant LSTM
        x = LayerNormalization()(x)
        x = self._apply_global_pooling_and_dropout(x, **kwargs)
        outputs = self._build_output_layer(x, init, kwargs.get("output_l2_reg", 1e-4))

        model = tf.keras.Model(inputs=inputs, outputs=outputs, name="cnn_attention_lstm_model_test")
        optimizer = tf.keras.optimizers.AdamW(
            learning_rate=kwargs.get("learning_rate", 1e-3),
            weight_decay=kwargs.get("weight_decay", 1e-4),
            amsgrad=True
        )

        loss_fn = self._get_loss_function(
            kwargs.get("loss_type", "correlation_mae_amplitude"),
            kwargs.get("delta_huber", 1),
            kwargs.get("gamma_val", 0.9),           # ✅ Correctement défini
            kwargs.get("alpha_val", 0.5),           # ✅ Correctement défini
            kwargs.get("lambda_std_val", 10),       # ✅ Correctement défini
            kwargs.get("lambda_scale_val", 10),     # ✅ Correctement défini
            kwargs.get("lambda_sign_val", 1.3),     # ✅ Correctement défini
            kwargs.get("trend_lag", 3),
            kwargs.get("w_pearson", 0.1),           # ✅ Correctement défini
            kwargs.get("horizon_hour", 3)           # Ajout du paramètre horizon_hour
        )
        model.compile(
            optimizer=optimizer,
            loss=loss_fn,
            metrics=[]
        )
        return model

    def build_high_performance_model(self, input_shape, **kwargs):
        """
        Modèle haute performance avec améliorations spécifiques pour les log returns.

        Améliorations:
        - Architecture plus profonde et adaptative
        - Normalisation par couches optimisée
        - Attention multi-échelle
        - Régularisation adaptative
        - Optimiseur et callbacks améliorés
        """
        init = tf.keras.initializers.GlorotUniform(seed=SEED)
        horizon_hour = kwargs.get("horizon_hour", 3)

        # Paramètres adaptatifs selon l'horizon
        base_filters = min(96 + (horizon_hour // 3) * 32, 192)
        lstm_units_1 = min(96 + (horizon_hour // 3) * 24, 160)
        lstm_units_2 = min(128 + (horizon_hour // 3) * 32, 224)
        mha_heads = max(4, min(horizon_hour // 2, 12))
        mha_key_dim = min(32 + (horizon_hour // 3) * 16, 96)

        # Régularisation adaptative
        dropout_rate = max(0.1, min(0.15 + (horizon_hour / 48) * 0.25, 0.4))
        l2_reg = max(1e-5, min(1e-4 * (horizon_hour / 12), 1e-3))

        inputs = Input(shape=input_shape, dtype='float32', name="input_enhanced")

        # ════════════════════════════════════════════════════════════════
        # 1. CNN Multi-Échelle Amélioré
        # ════════════════════════════════════════════════════════════════
        # Convolutions parallèles avec différentes tailles de noyaux
        conv1 = Conv1D(base_filters // 3, 1, activation='relu', padding='same',
                      kernel_initializer=init, name="conv_k1")(inputs)
        conv3 = Conv1D(base_filters, 3, activation='relu', padding='same',
                      kernel_initializer=init, name="conv_k3")(inputs)
        conv5 = Conv1D(base_filters // 2, 5, activation='relu', padding='same',
                      kernel_initializer=init, name="conv_k5")(inputs)
        conv7 = Conv1D(base_filters // 3, 7, activation='relu', padding='same',
                      kernel_initializer=init, name="conv_k7")(inputs)

        # Fusion des convolutions
        cnn_concat = Concatenate(name="cnn_multi_scale")([conv1, conv3, conv5, conv7])
        cnn_features = BatchNormalization(name="bn_cnn")(cnn_concat)
        cnn_features = Dropout(dropout_rate * 0.5, seed=SEED, name="dropout_cnn")(cnn_features)

        # Convolution de réduction dimensionnelle
        cnn_features = Conv1D(base_filters, 1, activation='relu', padding='same',
                             kernel_initializer=init, name="conv_reduce")(cnn_features)
        cnn_features = MaxPooling1D(2, name="maxpool_cnn")(cnn_features)

        # ════════════════════════════════════════════════════════════════
        # 2. LSTM Bidirectionnel Empilé avec Connexions Résiduelles
        # ════════════════════════════════════════════════════════════════
        # Première couche LSTM
        lstm1 = Bidirectional(
            LSTM(lstm_units_1, return_sequences=True, dropout=dropout_rate,
                 kernel_initializer=init, recurrent_initializer=init,
                 bias_initializer='zeros', seed=SEED),
            name="bilstm_1"
        )(cnn_features)

        # Connexion résiduelle avec projection
        cnn_proj1 = Dense(lstm_units_1 * 2, use_bias=False, name="cnn_proj1")(cnn_features)
        lstm1_res = Add(name="res_lstm1")([cnn_proj1, lstm1])
        lstm1_norm = LayerNormalization(epsilon=1e-6, name="norm_lstm1")(lstm1_res)

        # Deuxième couche LSTM
        lstm2 = Bidirectional(
            LSTM(lstm_units_2, return_sequences=True, dropout=dropout_rate,
                 kernel_initializer=init, recurrent_initializer=init,
                 bias_initializer='zeros', seed=SEED),
            name="bilstm_2"
        )(lstm1_norm)

        # Connexion résiduelle avec projection
        lstm1_proj = Dense(lstm_units_2 * 2, use_bias=False, name="lstm1_proj")(lstm1_norm)
        lstm2_res = Add(name="res_lstm2")([lstm1_proj, lstm2])
        lstm2_norm = LayerNormalization(epsilon=1e-6, name="norm_lstm2")(lstm2_res)

        # ════════════════════════════════════════════════════════════════
        # 3. Attention Multi-Tête avec Auto-Attention
        # ════════════════════════════════════════════════════════════════
        # Self-attention
        self_attn = MultiHeadAttention(
            num_heads=mha_heads, key_dim=mha_key_dim, dropout=dropout_rate,
            name="self_attention"
        )(lstm2_norm, lstm2_norm)

        # Connexion résiduelle et normalisation
        attn_res = Add(name="res_self_attn")([lstm2_norm, self_attn])
        attn_norm = LayerNormalization(epsilon=1e-6, name="norm_self_attn")(attn_res)

        # Cross-attention entre LSTM et CNN
        cross_attn = MultiHeadAttention(
            num_heads=max(2, mha_heads // 2), key_dim=mha_key_dim // 2, dropout=dropout_rate,
            name="cross_attention"
        )(attn_norm, cnn_features)

        # Fusion finale des attentions
        final_attn = Add(name="res_cross_attn")([attn_norm, cross_attn])
        final_attn = LayerNormalization(epsilon=1e-6, name="norm_final_attn")(final_attn)

        # ════════════════════════════════════════════════════════════════
        # 4. Feed-Forward Network (Transformer-style)
        # ════════════════════════════════════════════════════════════════
        ffn_dim = lstm_units_2 * 4  # Dimension FFN typique
        ffn = Dense(ffn_dim, activation='relu', kernel_regularizer=l2(l2_reg),
                   name="ffn_expand")(final_attn)
        ffn = Dropout(dropout_rate, seed=SEED, name="dropout_ffn")(ffn)
        ffn = Dense(lstm_units_2 * 2, kernel_regularizer=l2(l2_reg),
                   name="ffn_contract")(ffn)

        # Connexion résiduelle finale
        ffn_res = Add(name="res_ffn")([final_attn, ffn])
        ffn_norm = LayerNormalization(epsilon=1e-6, name="norm_ffn")(ffn_res)

        # ════════════════════════════════════════════════════════════════
        # 5. Couches de Sortie avec Régularisation
        # ════════════════════════════════════════════════════════════════
        # Global pooling avec attention pondérée
        pooled = GlobalAveragePooling1D(name="global_pool")(ffn_norm)

        # Couches denses avec dropout progressif
        dense1 = Dense(128, activation='relu', kernel_regularizer=l2(l2_reg),
                      kernel_initializer=init, name="dense1")(pooled)
        dense1 = Dropout(dropout_rate, seed=SEED, name="dropout_dense1")(dense1)

        dense2 = Dense(64, activation='relu', kernel_regularizer=l2(l2_reg),
                      kernel_initializer=init, name="dense2")(dense1)
        dense2 = Dropout(dropout_rate * 0.5, seed=SEED, name="dropout_dense2")(dense2)

        # Sortie finale
        outputs = Dense(1, activation=None, kernel_regularizer=l2(l2_reg),
                       kernel_initializer=init, bias_initializer='zeros',
                       name="output")(dense2)

        # ════════════════════════════════════════════════════════════════
        # 6. Compilation avec Optimiseur Amélioré
        # ════════════════════════════════════════════════════════════════
        model = tf.keras.Model(inputs=inputs, outputs=outputs, name="high_performance_model")

        # Optimiseur avec paramètres adaptatifs
        initial_lr = kwargs.get("learning_rate", 1e-3)
        adaptive_lr = max(5e-4, initial_lr * (1.0 - horizon_hour / 48))  # LR plus faible pour horizons longs

        optimizer = tf.keras.optimizers.AdamW(
            learning_rate=adaptive_lr,
            weight_decay=l2_reg * 10,  # Weight decay plus agressif
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-7,
            amsgrad=True
        )

        # Fonction de perte
        loss_fn = self._get_loss_function(
            kwargs.get("loss_type", "anti_suppression"),
            kwargs.get("delta_huber", 1),
            kwargs.get("gamma_val", 0.9),
            kwargs.get("alpha_val", 0.5),
            kwargs.get("lambda_std_val", 10),
            kwargs.get("lambda_scale_val", 10),
            kwargs.get("lambda_sign_val", 1.3),
            kwargs.get("trend_lag", 3),
            kwargs.get("w_pearson", 0.1),
            horizon_hour
        )

        model.compile(optimizer=optimizer, loss=loss_fn, metrics=[])

        return model

    def build_enhanced_cnn_attention_lstm_model(self, input_shape, **kwargs):
        """
        Enhanced model architecture with multi-scale CNN, feature fusion, and improved attention.

        Features:
        - Multi-scale CNN with parallel convolutions
        - Dual bidirectional LSTM layers
        - Enhanced multi-head attention with residual connections
        - Feature fusion from different layers
        - Adaptive regularization based on horizon

        Args:
            input_shape: Shape of input data (timesteps, features)
            **kwargs: Configuration parameters

        Returns:
            Compiled Keras model
        """
        # Initialize seed for reproducibility
        init = tf.keras.initializers.GlorotUniform(seed=SEED)

        # Extract key parameters with defaults
        horizon_hour = kwargs.get("horizon_hour", 3)

        # Scale parameters based on horizon length
        cnn_filters = kwargs.get("conv_filters", min(64 + (horizon_hour // 3) * 16, 128))
        lstm_units_1 = kwargs.get("lstm_units_1", min(64 + (horizon_hour // 3) * 16, 128))
        lstm_units_2 = kwargs.get("lstm_units_2", min(96 + (horizon_hour // 3) * 16, 192))
        mha_heads = kwargs.get("mha_heads", max(2, min(horizon_hour // 3, 8)))
        mha_key_dim = kwargs.get("mha_key_dim", min(16 + (horizon_hour // 3) * 8, 64))

        # Regularization parameters
        dropout_rate = kwargs.get("dropout_rate", min(0.1 + (horizon_hour / 24) * 0.3, 0.4))
        l2_reg = kwargs.get("output_l2_reg", 1e-4)
        use_batch_norm = kwargs.get("use_batch_norm", True)

        # Input layer
        inputs = Input(shape=input_shape, dtype='float32', name="input_layer_enhanced")

        # ════════════════════════════════════════════════════════════════
        # 1. Multi-Scale CNN Block
        # ════════════════════════════════════════════════════════════════
        # Parallel convolutions with different kernel sizes
        conv1 = Conv1D(cnn_filters // 2, kernel_size=1, activation='relu',
                      padding='same', kernel_initializer=init, name="conv1d_k1")(inputs)

        conv3 = Conv1D(cnn_filters, kernel_size=3, activation='relu',
                      padding='same', kernel_initializer=init, name="conv1d_k3")(inputs)

        conv5 = Conv1D(cnn_filters // 2, kernel_size=5, activation='relu',
                      padding='same', kernel_initializer=init, name="conv1d_k5")(inputs)

        # Concatenate different kernel sizes
        cnn_features = Concatenate(name="multi_scale_concat")([conv1, conv3, conv5])

        if use_batch_norm:
            cnn_features = BatchNormalization(name="batch_norm_cnn")(cnn_features)

        # Apply pooling
        cnn_features = MaxPooling1D(pool_size=2, name="max_pool_cnn")(cnn_features)

        # Store CNN features for later fusion
        cnn_output = cnn_features

        # ════════════════════════════════════════════════════════════════
        # 2. First LSTM Layer (Bidirectional)
        # ════════════════════════════════════════════════════════════════
        lstm_layer_1 = LSTM(
            lstm_units_1, return_sequences=True,
            kernel_initializer=init, recurrent_initializer=init,
            bias_initializer='zeros', dropout=dropout_rate,
            seed=SEED, name="lstm_1_enhanced"
        )

        lstm_features = Bidirectional(lstm_layer_1, name="bidir_lstm_1")(cnn_features)

        # Store first LSTM features for later fusion
        lstm1_output = lstm_features

        # Always project CNN features to match LSTM dimension for residual connection
        # This avoids tensor shape comparison in Python conditionals
        cnn_projected = Dense(lstm_units_1 * 2, use_bias=False, name="cnn_projection")(cnn_features)
        lstm_features = Add(name="residual_lstm1")([cnn_projected, lstm_features])

        # ════════════════════════════════════════════════════════════════
        # 3. Second LSTM Layer (Bidirectional)
        # ════════════════════════════════════════════════════════════════
        lstm_layer_2 = LSTM(
            lstm_units_2, return_sequences=True,
            kernel_initializer=init, recurrent_initializer=init,
            bias_initializer='zeros', dropout=dropout_rate,
            seed=SEED, name="lstm_2_enhanced"
        )

        lstm_features = Bidirectional(lstm_layer_2, name="bidir_lstm_2")(lstm_features)

        # Store second LSTM features for later fusion
        lstm2_output = lstm_features

        # Always project LSTM1 features to match LSTM2 dimension for residual connection
        # This avoids tensor shape comparison in Python conditionals
        lstm1_projected = Dense(lstm_units_2 * 2, use_bias=False, name="lstm1_projection")(lstm1_output)
        lstm_features = Add(name="residual_lstm2")([lstm1_projected, lstm_features])

        # ════════════════════════════════════════════════════════════════
        # 4. Enhanced Multi-Head Attention
        # ════════════════════════════════════════════════════════════════
        # Layer normalization before attention
        norm_lstm = LayerNormalization(epsilon=1e-6, name="norm_pre_mha")(lstm_features)

        # Multi-head attention
        attention_output = MultiHeadAttention(
            num_heads=mha_heads,
            key_dim=mha_key_dim,
            dropout=dropout_rate,
            name="mha_enhanced"
        )(norm_lstm, norm_lstm)

        # Dropout and residual connection
        attention_output = Dropout(dropout_rate, seed=SEED, name="dropout_mha")(attention_output)
        attention_output = Add(name="residual_mha")([lstm_features, attention_output])

        # Second normalization
        attention_output = LayerNormalization(epsilon=1e-6, name="norm_post_mha")(attention_output)

        # Feed-forward network (as in Transformer)
        # Use a fixed size for the FFN to avoid tensor shape operations
        ffn = Dense(256, activation='relu', name="ffn_1")(attention_output)
        ffn = Dropout(dropout_rate, seed=SEED, name="dropout_ffn")(ffn)
        ffn = Dense(lstm_units_2 * 2, name="ffn_2")(ffn)  # Match LSTM2 output dimension

        # Final residual connection
        attention_output = Add(name="residual_ffn")([attention_output, ffn])
        attention_output = LayerNormalization(epsilon=1e-6, name="norm_final")(attention_output)

        # Store attention features for fusion
        attn_output = attention_output

        # ════════════════════════════════════════════════════════════════
        # 5. Feature Fusion
        # ════════════════════════════════════════════════════════════════
        # Global pooling for each feature stream
        cnn_pooled = GlobalAveragePooling1D(name="gap_cnn")(cnn_output)
        lstm1_pooled = GlobalAveragePooling1D(name="gap_lstm1")(lstm1_output)
        lstm2_pooled = GlobalAveragePooling1D(name="gap_lstm2")(lstm2_output)
        attn_pooled = GlobalAveragePooling1D(name="gap_attn")(attn_output)

        # Concatenate all features
        all_features = Concatenate(name="all_features_concat")([
            cnn_pooled, lstm1_pooled, lstm2_pooled, attn_pooled
        ])

        # Apply dropout to combined features
        all_features = Dropout(dropout_rate, seed=SEED, name="dropout_fusion")(all_features)

        # ════════════════════════════════════════════════════════════════
        # 6. Output Layer
        # ════════════════════════════════════════════════════════════════
        # Final dense layers
        x = Dense(
            lstm_units_1,
            activation='relu',
            kernel_initializer=init,
            kernel_regularizer=l2(l2_reg),
            name="dense_final_1"
        )(all_features)

        x = Dropout(dropout_rate, seed=SEED, name="dropout_final")(x)

        # Output layer with amplitude scaling
        outputs = Dense(
            1,
            activation=None,
            kernel_initializer=init,
            bias_initializer='zeros',
            kernel_regularizer=l2(l2_reg),
            name="output_final"
        )(x)

        # Apply amplitude constraint (scale down predictions)
        amplitude_scale = kwargs.get("amplitude_scale", 0.1)  # Scale down by default
        outputs = outputs * amplitude_scale

        # ════════════════════════════════════════════════════════════════
        # 7. Model Compilation
        # ════════════════════════════════════════════════════════════════
        model = tf.keras.Model(inputs=inputs, outputs=outputs, name="enhanced_cnn_attention_lstm")

        # Optimizer with learning rate schedule
        optimizer = tf.keras.optimizers.AdamW(
            learning_rate=kwargs.get("learning_rate", 1e-3),
            weight_decay=kwargs.get("weight_decay", 1e-4),
            amsgrad=True
        )
        # Loss function
        loss_fn = self._get_loss_function(
            kwargs.get("loss_type", "anti_suppression"),
            kwargs.get("delta_huber", 1),
            kwargs.get("gamma_val", 0.9),
            kwargs.get("alpha_val", 0.5),
            kwargs.get("lambda_std_val", 10),
            kwargs.get("lambda_scale_val", 10),
            kwargs.get("lambda_sign_val", 1.3),
            kwargs.get("trend_lag", 3),
            kwargs.get("w_pearson", 0.1),
            horizon_hour
        )

        # Compile model
        model.compile(
            optimizer=optimizer,
            loss=loss_fn,
            metrics=[]
        )

        return model

    def multihead_attention_block_test(self, inputs,
                                   num_heads=4,
                                   key_dim=32,
                                   dropout_rate=0.1,
                                   name_prefix="mha_block_test"):
        # 🧽 Normalisation préalable (comme dans les Transformers)
        norm_inputs = LayerNormalization(epsilon=1e-6, name=f"{name_prefix}_norm")(inputs)

        # 🎯 Attention multi-têtes en self-attention : Q = K = V = inputs
        attention_output = MultiHeadAttention(
            num_heads=num_heads,
            key_dim=key_dim,
            dropout=dropout_rate,
            name=f"{name_prefix}_mha"
        )(norm_inputs, norm_inputs)

        # 🔁 Résiduel + normalisation
        attention_output = Dropout(dropout_rate, seed=SEED, name=f"{name_prefix}_dropout")(attention_output)
        out = Add(name=f"{name_prefix}_residual")([inputs, attention_output])
        out = LayerNormalization(epsilon=1e-6, name=f"{name_prefix}_output_norm")(out)

        return out



    def evaluate_model_result(
            self,
            y_true,
            y_pred,
            y_true_train=None,
            y_pred_train=None,
            weights=None,
            trend_lag=1,
            loss_val=None
        ):
        """
        Évaluation robuste pour day trading.
        Mesure la direction, la dynamique et l'amplitude des prédictions.
        """
        try:
            # ✅ Conversion en np.array et vérification des NaN
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)

            if np.isnan(y_true).any() or np.isnan(y_pred).any():
                raise ValueError("y_true ou y_pred contient des NaN")

            if y_true_train is not None and y_pred_train is not None:
                y_true_train = np.array(y_true_train)
                y_pred_train = np.array(y_pred_train)
                if np.isnan(y_true_train).any() or np.isnan(y_pred_train).any():
                    raise ValueError("y_true_train ou y_pred_train contient des NaN")

            # 📏 Métriques de base
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))

            # ✅ R² avec scaling
            scale_factor = 1000
            y_true_scaled = y_true * scale_factor
            y_pred_scaled = y_pred * scale_factor
            ss_total = np.sum((y_true_scaled - np.mean(y_true_scaled)) ** 2)
            ss_residual = np.sum((y_true_scaled - y_pred_scaled) ** 2)
            r2 = 1 - (ss_residual / (ss_total + 1e-8))

            # ✅ Corrélation Pearson (protégé)
            if np.std(y_true) < 1e-8 or np.std(y_pred) < 1e-8:
                pearson_corr = 0.0
            else:
                pearson_corr = np.corrcoef(y_true, y_pred)[0, 1]

            # ✅ Direction & tendance
            if len(y_true) <= trend_lag or len(y_pred) <= trend_lag:
                raise ValueError("Trend lag trop grand par rapport à la taille des données.")

            delta_true = y_true[trend_lag:] - y_true[:-trend_lag]
            delta_pred = y_pred[trend_lag:] - y_pred[:-trend_lag]
            trend_directional_accuracy = np.mean(np.sign(delta_true) == np.sign(delta_pred))

            # ✅ Calibration d'Amplitude
            std_y = max(np.std(y_true), 1e-8)
            std_y_pred = np.std(y_pred)
            std_penalty = np.square((std_y_pred / std_y) - 1.0)

            # ✅ Amplitude Loss (erreur proportionnelle)
            errors = y_pred - y_true
            relative_errors = errors / np.maximum(np.abs(y_true), 1e-6)
            amplitude_loss = np.sqrt(np.mean(np.square(relative_errors)))

            # ✅ Overfitting
            overfitting = None
            if y_true_train is not None and y_pred_train is not None:
                rmse_train = np.sqrt(mean_squared_error(y_true_train, y_pred_train))
                overfitting = rmse - rmse_train

            # ⚙️ Poids
            weights = weights or {
                "trend_directional_accuracy": 0.4,
                "amplitude_loss": 0.3,
                "std_penalty": 0.2,
                "correlation": 0.1,
            }

            # ✅ Score combiné
            combined_score = (
                weights["trend_directional_accuracy"] * trend_directional_accuracy +
                weights["amplitude_loss"] * np.exp(-amplitude_loss) +
                weights["std_penalty"] * np.exp(-std_penalty) +
                weights["correlation"] * max(0, pearson_corr)
            ) * 100

            return {
                "loss_val": loss_val,
                "combined_score": combined_score,
                "rmse": rmse,
                "mae": mae,
                "pearson_corr": pearson_corr,
                "trend_directional_accuracy": trend_directional_accuracy,
                "std_y": std_y,
                "std_y_pred": std_y_pred,
                "std_penalty": std_penalty,
                "amplitude_loss": amplitude_loss,
                "r2": r2,
                "overfitting": overfitting
            }

        except Exception as e:
            raise ValueError(f"❌ Erreur pendant le calcul du score (day trading) : {e}")




    def should_prune_trial(self, avg_metrics, trial, uid, horizon, strict_pruning=True, baseline_score=0):
        trial_id = f"Trial #{trial.number + 1}"
        base_log = f"{uid} - {horizon['name']} - {trial_id}"

        #directional_accuracy = avg_metrics.get("directional_accuracy", 0)
        trend_directional_accuracy = avg_metrics.get("trend_directional_accuracy", 0)
        overfitting = avg_metrics.get("overfitting", 1.0)
        combined_score = avg_metrics.get("combined_score", 0)
        variance_ratio = avg_metrics.get("variance_ratio", 1.0)
        sign_balance = avg_metrics.get("sign_balance", 0.0)
        r2 = avg_metrics.get("r2", -1)
        pearson_corr = avg_metrics.get("pearson_corr", -1)

        variance_threshold = 0.1
        r2_threshold = -0.5
        pearson_corr_threshold = 0
        sign_balance_threshold = 0.3

        if hasattr(trial, "study") and trial.study and trial.number > 0:
            #best_eval = trial.study.best_trial.user_attrs.get("evaluation", {})
            #best_score = best_eval.get("combined_score", 100)
            #startup_trials = trial.study.user_attrs.get("startup_trials", 20)

            '''
            # 🌟 Early Weak Detection même durant startup_trials
            if trial.number <= startup_trials:
                if trend_directional_accuracy < 0.4:
                    raise optuna.TrialPruned()
            '''

            # 🔒 Pruning strict normal
            if strict_pruning is True:
                if pearson_corr < pearson_corr_threshold:
                    raise optuna.TrialPruned()

                if trend_directional_accuracy < 0.45:
                    raise optuna.TrialPruned()

                if overfitting > 0.12 or overfitting < -0.15:
                    raise optuna.TrialPruned()

                '''

                if variance_ratio < variance_threshold:
                    raise optuna.TrialPruned()


                if sign_balance > sign_balance_threshold:
                    raise optuna.TrialPruned()


                threshold = best_score - 2
                if combined_score < threshold:
                    raise optuna.TrialPruned()
                '''

        # 🧪 STRATEGIE LIGHT
        elif strict_pruning is False:
            if overfitting > 0.1 or overfitting < -0.1:
                raise optuna.TrialPruned()



    def find_optimal_time_shift(self, y_true, y_pred, max_shift=10):
        """
        Cherche le décalage temporel optimal entre y_true et y_pred.
        Renvoie le décalage avec la meilleure corrélation.
        """
        best_corr = -100
        best_shift = 0
        for shift in range(-max_shift, max_shift + 1):
            if shift < 0:
                shifted = y_pred[-shift:]  # shift négatif = y_pred est en avance
                ref = y_true[:len(shifted)]
            elif shift > 0:
                shifted = y_pred[:-shift]
                ref = y_true[shift:]
            else:
                shifted = y_pred
                ref = y_true
            corr = np.corrcoef(ref, shifted)[0, 1]
            if corr > best_corr:
                best_corr = corr
                best_shift = shift
        return best_shift, best_corr




    def execute_training_model(self, uid, horizon, df, model_params, selected_features=None, save_model=True):
        base_log = f"{uid} - {horizon['name']}"
        model_dir = "/home/<USER>/cryptobot/models"
        os.makedirs(model_dir, exist_ok=True)

        # Cleanup mémoire
        self.cleanup_training_memory(locals())

        try:
            # Vérification de la cible
            if "log_return" not in df.columns:
                self.logger.send_log(f"❌ - {base_log} - log_return manquant dans df", "error")
                return

            # Préparation X et y
            df_clean = df.drop(columns=["timestamp", "future_log_price", "highPrice", "lowPrice", "lastPrice", "past_base_price"], errors="ignore")
            X = df_clean.drop(columns=["log_return"]).astype(np.float32)
            y = df_clean["log_return"].values.astype(np.float32)

            if selected_features:
                X = self.keep_only_features(X, selected_features)

            if len(X) < 100:
                self.logger.send_log(f"❌ - {base_log} - Pas assez de données : {len(X)}", "warning")
                return

            # Séquences
            X_seq, y_seq = self.create_sequences_shuffled(
                X.values, y, n_steps=model_params.get("n_steps", 30), shuffle_blocks=False
            )
            if len(X_seq) < 150:
                self.logger.send_log(f"❌ - {base_log} - Trop peu de séquences : {len(X_seq)}", "warning")
                return

            # Split train/val
            split_idx = int(0.8 * len(X_seq))
            X_train, X_val = X_seq[:split_idx], X_seq[split_idx:]
            y_train, y_val = y_seq[:split_idx], y_seq[split_idx:]

            # Scaling X
            n_samp, n_steps, n_feat = X_train.shape
            scaler_X = StandardScaler()
            X_tr_flat = X_train.reshape(-1, n_feat)
            X_tr_s = scaler_X.fit_transform(X_tr_flat)
            X_train_scaled = X_tr_s.reshape(n_samp, n_steps, n_feat)
            X_val_scaled = scaler_X.transform(X_val.reshape(-1, n_feat)).reshape(X_val.shape)

            # Scaling y
            scaler_y = StandardScaler()
            y_train_s = scaler_y.fit_transform(y_train.reshape(-1,1)).flatten()
            y_val_s   = scaler_y.transform(y_val.reshape(-1,1)).flatten()

            # Calcul delta_huber
            delta_huber = self.compute_delta_huber(
                y_train_s, multiplier=2.0, min_value=1e-4, max_value=0.1, base_log=base_log
            )

            # Paramètres
            self.logger.send_log(f"✅ - {base_log} - Model Params = {model_params}", "info")
            params = {
                **model_params,
                'delta_huber': delta_huber,
                'horizon_hour': horizon['horizon_hour']
            }

            # Construction du modèle
            input_shape = (model_params['n_steps'], n_feat)

            # Sélection du modèle selon les paramètres
            model_type = model_params.get('model_type', 'high_performance')  # 'enhanced', 'high_performance', 'basic'

            if model_type == 'high_performance':
                self.logger.send_log(f"🚀 - {base_log} - Using HIGH PERFORMANCE model", "info")
                model = self.build_high_performance_model(input_shape, **params)
            elif model_type == 'enhanced':
                self.logger.send_log(f"🚀 - {base_log} - Using enhanced CNN-Attention-LSTM model", "info")
                model = self.build_enhanced_cnn_attention_lstm_model(input_shape, **params)
            else:
                self.logger.send_log(f"🚀 - {base_log} - Using basic CNN-Attention-LSTM model", "info")
                model = self.build_cnn_attention_lstm_model_test(input_shape, **params)

            # ✅ Callbacks Améliorés
            # 1. ReduceLROnPlateau plus agressif
            reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.6,  # Réduction moins agressive
                patience=8,   # Plus patient
                min_lr=1e-7,  # LR minimum plus bas
                min_delta=1e-6,  # Seuil d'amélioration
                cooldown=3,   # Période de refroidissement
                verbose=0
            )

            # 2. Learning Rate Scheduler adaptatif
            def adaptive_lr_scheduler(epoch, lr):
                horizon_factor = max(0.5, 1.0 - horizon['horizon_hour'] / 48)

                if epoch < 3:
                    return lr  # Warmup
                elif epoch < 10:
                    return lr * 0.95  # Décroissance douce
                elif epoch < 25:
                    return lr * 0.9   # Décroissance modérée
                else:
                    return lr * 0.85 * horizon_factor  # Décroissance adaptée à l'horizon

            lr_callback = tf.keras.callbacks.LearningRateScheduler(adaptive_lr_scheduler, verbose=0)

            # 3. Early Stopping adaptatif
            base_patience = model_params.get('patience', 8)
            adaptive_patience = max(base_patience, min(base_patience + horizon['horizon_hour'] // 3, 20))

            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=adaptive_patience,
                restore_best_weights=True,
                min_delta=1e-6,
                mode='min',
                verbose=0
            )

            # 4. Callback personnalisé pour monitoring
            class TrainingMonitor(tf.keras.callbacks.Callback):
                def __init__(self, logger, base_log):
                    super().__init__()
                    self.logger = logger
                    self.base_log = base_log
                    self.best_val_loss = float('inf')
                    self.plateau_count = 0

                def on_epoch_end(self, epoch, logs=None):
                    val_loss = logs.get('val_loss', 0)
                    train_loss = logs.get('loss', 0)
                    lr = logs.get('lr', 0)

                    if val_loss < self.best_val_loss:
                        self.best_val_loss = val_loss
                        self.plateau_count = 0
                    else:
                        self.plateau_count += 1

                    # Log périodique
                    if (epoch + 1) % 10 == 0:
                        ratio = val_loss / train_loss if train_loss > 0 else float('inf')
                        self.logger.send_log(
                            f"📈 - {self.base_log} - Epoch {epoch+1}: "
                            f"train_loss={train_loss:.6f}, val_loss={val_loss:.6f}, "
                            f"ratio={ratio:.3f}, lr={lr:.2e}, plateau={self.plateau_count}",
                            "debug"
                        )

            monitor = TrainingMonitor(self.logger, base_log)

            # Entraînement avec callbacks améliorés
            history = model.fit(
                X_train_scaled, y_train_s,
                validation_data=(X_val_scaled, y_val_s),
                batch_size=model_params.get('batch_size', 64),
                epochs=model_params.get('epochs', 50),
                verbose=0,
                shuffle=False,
                callbacks=[reduce_lr, lr_callback, early_stopping, monitor]
            )

            # Analyse détaillée de l'entraînement
            train_loss = history.history.get('loss', [])
            val_loss = history.history.get('val_loss', [])
            learning_rates = history.history.get('lr', [])

            if train_loss and val_loss:
                final_train_loss = train_loss[-1]
                final_val_loss = val_loss[-1]
                min_val_loss = min(val_loss)
                best_epoch = val_loss.index(min_val_loss) + 1

                # Détection d'overfitting/underfitting
                loss_ratio = final_val_loss / final_train_loss if final_train_loss > 0 else float('inf')

                #self.logger.send_log(f"📊 - {base_log} - Entraînement terminé:", "info")
                #self.logger.send_log(f"📊 - {base_log} - Epochs: {len(train_loss)}, Meilleure époque: {best_epoch}", "info")
                #self.logger.send_log(f"📊 - {base_log} - Loss finale - Train: {final_train_loss:.6f}, Val: {final_val_loss:.6f}", "info")
                #self.logger.send_log(f"📊 - {base_log} - Meilleure val_loss: {min_val_loss:.6f}", "info")
                #self.logger.send_log(f"📊 - {base_log} - Ratio val/train: {loss_ratio:.3f}", "info")

                if loss_ratio > 1.5:
                    self.logger.send_log(f"⚠️ - {base_log} - Possible overfitting détecté (ratio: {loss_ratio:.3f})", "warning")
                elif final_train_loss > 0.01:
                    self.logger.send_log(f"⚠️ - {base_log} - Possible underfitting (train_loss élevée: {final_train_loss:.6f})", "warning")

                # Analyse de la convergence
                if len(train_loss) >= 10:
                    recent_improvement = (train_loss[-10] - train_loss[-1]) / train_loss[-10]
                    if recent_improvement < 0.01:
                        self.logger.send_log(f"⚠️ - {base_log} - Convergence lente (amélioration: {recent_improvement:.3%})", "warning")

                self.logger.send_log(f"📊 - {base_log} - Learning Rates: {learning_rates[-5:] if len(learning_rates) > 5 else learning_rates}", "info")

            # Prédiction et inversion du scaling
            y_pred_s = model.predict(X_val_scaled, verbose=0).flatten()
            y_final_pred = scaler_y.inverse_transform(y_pred_s.reshape(-1,1)).flatten()
            y_final_true = y_val

            '''
            self.visualize_layer_activations(
                model,
                X_val_scaled[0],  # ✅ Utilisation de la première séquence de validation
                base_log=base_log,
                base_path="/home/<USER>/debug_models",
                layer_names=[
                    "conv1d_initial_test",
                    "batch_norm_initial_test",
                    "max_pool_initial_test",
                    "lstm_1_test",
                    "lstm_2_test",
                    "mha_test_mha",
                    "global_pool_test",
                    "output_final"
                ]
            )
            '''

            # Diagnostics
            self.plot_training_diagnostics(
                y_true=y_final_true,
                y_pred=y_final_pred,
                fold_number='FINAL',
                base_log=base_log,
                base_path="/home/<USER>/debug_models"
            )

            # Évaluation
            # Inverse scaling sur train preds
            trend_lag = model_params.get('trend_lag', 1)

            y_train_pred_s = model.predict(X_train_scaled, verbose=0).flatten()
            y_train_pred = scaler_y.inverse_transform(y_train_pred_s.reshape(-1,1)).flatten()
            metrics = self.evaluate_model_result(
                y_true=y_final_true, y_pred=y_final_pred,
                y_true_train=y_train, y_pred_train=y_train_pred,
                trend_lag=trend_lag
            )
            self.logger.send_log(f"📌 - {base_log} - {metrics}", "info")

            # Sauvegarde modèle et scalers (conditionnelle)
            if save_model:
                model.save(os.path.join(model_dir, f"{uid}_{horizon['name']}_regression.keras"))
                joblib.dump(scaler_X, os.path.join(model_dir, f"{uid}_{horizon['name']}_scaler_X.gz"))
                joblib.dump(scaler_y, os.path.join(model_dir, f"{uid}_{horizon['name']}_scaler_y.gz"))
                self.logger.send_log(f"💾 - {base_log} - Modèle et scalers sauvegardés", "info")
            else:
                self.logger.send_log(f"⏭️ - {base_log} - Sauvegarde du modèle ignorée (save_model=False)", "info")

            del model, scaler_X, scaler_y, df, X, y, X_train, X_val, y_train, y_val, X_train_scaled, X_val_scaled, y_train_s, y_val_s, y_train_pred_s, y_pred_s, y_final_pred, y_final_true, y_train_pred
            return metrics

        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - Erreur : {e}", "error")
            return None




    def execute_optimize_model_regression_simple_split(
        self, trial, horizon, uid, df,
        selected_features=None,
        model_params=None,
        strict_pruning=True,
        baseline_score=0
    ):
        trial_id = f"Trial #{trial.number + 1}"
        base_log = f"{uid} - {horizon['name']} - {trial_id}"

        self.cleanup_training_memory(locals())

        # 1) Préparation X / y
        if "log_return" not in df.columns:
            self.logger.send_log(f"❌ - {base_log} - Log_return absent", "error")
            raise optuna.TrialPruned()

        df = df.drop(columns=[
            "timestamp","future_log_price","highPrice","lowPrice","lastPrice","past_base_price"
        ], errors="ignore")
        X = df.drop(columns=["log_return"]).astype(np.float32)
        y = df["log_return"].values.astype(np.float32)

        if selected_features:
            missing = [f for f in selected_features if f not in X.columns]
            if missing:
                self.logger.send_log(f"❌ - {base_log} - Feature manquante : {missing}", "warning")
                raise optuna.TrialPruned()
            X = X[selected_features]

        if len(X) < 100:
            self.logger.send_log(f"❌ - {base_log} - Trop peu de données", "warning")
            raise optuna.TrialPruned()

        # 2) Séquences
        try:
            X_seq, y_seq = self.create_sequences_shuffled(
                X.values, y, model_params['n_steps'], shuffle_blocks=False
            )
        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - create_sequences error: {e}", "error")
            raise optuna.TrialPruned()

        if len(X_seq) < 150:
            self.logger.send_log(f"❌ - {base_log} - Peu de séquences", "warning")
            raise optuna.TrialPruned()

        # 3) Split train/val
        split_index = int(0.8 * len(X_seq))
        X_train, X_val = X_seq[:split_index], X_seq[split_index:]
        y_train, y_val = y_seq[:split_index], y_seq[split_index:]

        # 4) Scaling X
        n_tr, n_steps, n_feat = X_train.shape
        scaler_X = StandardScaler()
        X_tr_flat = X_train.reshape(-1, n_feat)
        X_tr_s    = scaler_X.fit_transform(X_tr_flat)
        X_train_scaled = X_tr_s.reshape(n_tr, n_steps, n_feat)
        X_val_scaled   = scaler_X.transform(X_val.reshape(-1, n_feat)).reshape(X_val.shape)

        # 5) Scaling y
        scaler_y = StandardScaler()
        y_train_s = scaler_y.fit_transform(y_train.reshape(-1,1)).flatten()
        y_val_s   = scaler_y.transform(  y_val.reshape(-1,1)  ).flatten()

        # 6) Calcul delta_huber sur y_train_s
        delta_huber = self.compute_delta_huber(
            y_train_s,
            multiplier=2.0,
            min_value=1e-4,
            max_value=0.1,
            base_log=base_log
        )

        # 7) Construction et entraînement du modèle
        input_shape = (model_params['n_steps'], n_feat)
        params = {
            **model_params,
            'delta_huber': delta_huber,
            'horizon_hour': horizon['horizon_hour']  # Ajout du paramètre horizon_hour
        }

        try:
            # Use the enhanced model if specified in parameters
            use_enhanced_model = model_params.get('use_enhanced_model', True)
            if use_enhanced_model:
                self.logger.send_log(f"🚀 - {base_log} - Using enhanced CNN-Attention-LSTM model", "info")
                model = self.build_enhanced_cnn_attention_lstm_model(input_shape, **params)
            else:
                model = self.build_cnn_attention_lstm_model_test(input_shape, **params)
        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - build model error: {e}", "error")
            raise optuna.TrialPruned()



        # ✅ Callbacks : ReduceLROnPlateau + LearningRateScheduler + EarlyStopping
        reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=10,
            min_lr=1e-6,
            verbose=0
        )

        def lr_scheduler(epoch, lr):
            if epoch < 5:
                return lr
            elif epoch < 15:
                return lr * 0.8
            else:
                return lr * 0.5

        lr_callback = tf.keras.callbacks.LearningRateScheduler(lr_scheduler, verbose=0)

        patience = model_params.get('patience', 5)

        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=patience,
            restore_best_weights=True,
            verbose=0
        )

        # Entraînement
        history = model.fit(
            X_train_scaled, y_train_s,
            validation_data=(X_val_scaled, y_val_s),
            batch_size=model_params.get('batch_size', 64),
            epochs=model_params.get('epochs', 50),  # ⚡ Ajusté à 50 pour un meilleur ajustement
            verbose=0,
            shuffle=False,
            callbacks=[reduce_lr, lr_callback, early_stopping]
        )

        # 8) Prédictions et inversion du scaling
        y_val_pred_s   = model.predict(X_val_scaled, verbose=0).flatten()
        y_train_pred_s = model.predict(X_train_scaled, verbose=0).flatten()

        y_val_pred   = scaler_y.inverse_transform(y_val_pred_s.reshape(-1,1)).flatten()
        y_train_pred = scaler_y.inverse_transform(y_train_pred_s.reshape(-1,1)).flatten()

        # Récupération de la loss_val
        loss_val = model.evaluate(X_val_scaled, y_val_pred_s, verbose=0)

        trend_lag = model_params.get('trend_lag', 1)
        # 9) Évaluation
        metrics = self.evaluate_model_result(
            y_true=y_val,
            y_pred=y_val_pred,
            y_true_train=y_train,
            y_pred_train=y_train_pred,
            trend_lag=trend_lag,
            loss_val=loss_val
        )

        self.plot_training_diagnostics(
            y_true=y_val,
            y_pred=y_val_pred,
            fold_number=trial_id,
            base_log=base_log,
            base_path="/home/<USER>/debug_optim"
        )

        # 10) Logging & pruning
        metrics.update({'uid': uid, 'horizon': horizon['name']})
        self.logger.send_raw_data_log(metrics, metric="optimization_debug")

        combined_score = metrics.get("combined_score", 0)
        trial.set_user_attr("evaluation", metrics)
        trial.set_user_attr("combined_score", loss_val)
        self.should_prune_trial(metrics, trial, uid, horizon, strict_pruning, baseline_score)
        self.logger.send_log(f"📌 - {base_log} - {metrics}", "info")

        return combined_score







    def optimize_model_regression_simple_split(self, trial, horizon, uid, df,
                                           selected_features=None,
                                           model_params=None,
                                           strict_pruning=True,
                                           baseline_score=0):
        queue = Queue()

        exec_args = {
            "trial": trial,
            "horizon": horizon,
            "uid": uid,
            "df": df,
            "selected_features": selected_features,
            "model_params": model_params,
            "strict_pruning": strict_pruning,
            "baseline_score": baseline_score,
            "logger_config": {
                "service": self.logger.service,  # tu peux adapter selon ta classe GrafanaUtils
            }
        }

        process = Process(
            target=optimize_model_worker,
            args=(queue, exec_args)
        )
        process.start()
        process.join()

        if not queue.empty():
            status, result, user_attrs = queue.get()

            if status == "success":
                for k, v in (user_attrs or {}).items():
                    trial.set_user_attr(k, v)
                return result
            elif status == "pruned":
                raise optuna.TrialPruned()
            else:
                self.logger.send_log(f"❌ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Erreur dans le sous-processus :\n{result}","error")
                raise optuna.TrialPruned()
        else:
            self.logger.send_log(f"❌ - {uid} - {horizon['name']} - Trial #{trial.number + 1} - Queue vide (échec de subprocess)","error")
            raise optuna.TrialPruned()




    def train_regression_model(self, uid, horizon, df, hyperparams, selected_features=None, save_model=True):

        queue = Queue()

        exec_args = {
            "uid": uid,
            "horizon": horizon,
            "df": df,
            "model_params": hyperparams,
            "selected_features": selected_features,
            "save_model": save_model,
            "logger_config": {
                "service": self.logger.service,
            }
        }

        process = Process(
            target=train_model_worker,
            args=(queue, exec_args)
        )
        process.start()
        process.join()

        if not queue.empty():
            status, result = queue.get()
            if status == "success":
                return result  # Retourne les métriques au lieu de True
            else:
                self.logger.send_log(f"❌ - {uid} - {horizon} - Erreur entraînement dans subprocess :\n{result}", "error")
                return None
        else:
            self.logger.send_log(f"❌ - {uid} - {horizon} - Subprocess training échoué (queue vide)", "error")
            return None





