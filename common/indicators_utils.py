import talib
import pandas as pd
import numpy as np
import gc
import os
from scipy.stats import zscore
from sklearn.linear_model import LinearRegression
from common.grafana_utils import GrafanaUtils
from common.mongo_utils import MongoUtils
from common.models_utils import ModelsUtils

os.makedirs("/home/<USER>/debug_df", exist_ok=True)

class IndicatorsUtils:
    def __init__(self, logger=None, service="cryptobot"):
        self.logger = GrafanaUtils(service)
        self.mongo = MongoUtils()
        self.mongo.connect(service)
        models = ModelsUtils(service)

    def merge_rss_data(self, df, crypto, uid):
            """
            Fusionne les données RSS avec le DataFrame principal, alignées par timestamp
            et symbol. Si 'GENERAL' est présent, il est appliqué à tous les symbols.
            """
            try:
                collection = self.mongo.db["news_status"]
                rss_docs = list(collection.find({"$or": [{"crypto": crypto}, {"crypto": "GENERAL"}]}))

                if not rss_docs:
                    self.logger.send_log(f"⚠️ - {uid} - Aucun RSS trouvé pour {crypto}", "warning")
                    return df

                rss_df = pd.DataFrame(rss_docs)
                rss_df["window_start"] = pd.to_datetime(rss_df["window_start"])
                rss_df["window_end"] = pd.to_datetime(rss_df["window_end"])

                def match_rss(ts):
                    matches = rss_df[(rss_df["window_start"] <= ts) & (rss_df["window_end"] > ts)]
                    if matches.empty:
                        return {}
                    specific = matches[matches["crypto"] == crypto]
                    if not specific.empty:
                        return specific.iloc[-1].to_dict()
                    general = matches[matches["crypto"] == "GENERAL"]
                    if not general.empty:
                        return general.iloc[-1].to_dict()
                    return {}

                df["rss_window"] = df["timestamp"].apply(match_rss)

                for col in [
                    "nb_articles", "mean_sentiment", "var_sentiment",
                    "mean_geo_score", "max_geo_score", "trend_encoded",
                    "impact_low", "impact_moderate", "impact_high"
                ]:
                    df[col] = df["rss_window"].apply(lambda x: x.get(col) if isinstance(x, dict) else np.nan)

                # Flatten des embeddings
                df["content_embedding"] = df["rss_window"].apply(lambda x: x.get("content_embedding") if isinstance(x, dict) else None)
                max_len = df["content_embedding"].dropna().apply(len).max() or 0
                for i in range(max_len):
                    df[f"embed_{i}"] = df["content_embedding"].apply(lambda x: x[i] if isinstance(x, list) and len(x) > i else np.nan)

                # Moyenne des embeddings (pour réseau type LSTM/CNN)
                embed_cols = [f"embed_{i}" for i in range(max_len)]
                df["embed_mean"] = df[embed_cols].mean(axis=1)
                # Standardisation des colonnes à forte variation
                cols_to_standardize = ["embed_mean", "nb_articles", "mean_sentiment", "var_sentiment", "mean_geo_score", "max_geo_score"]
                for col in cols_to_standardize:
                    if col in df.columns:
                        mean = df[col].mean()
                        std = df[col].std()
                        if std > 0:
                            df[col + "_z"] = (df[col] - mean) / std
                        else:
                            df[col + "_z"] = 0
                # Log de qualité des embeddings
                nan_ratio = df[embed_cols].isna().mean().mean()
                if nan_ratio > 0.2:
                    self.logger.send_log(f"⚠️ - {uid} - Plus de 20% de NaN en moyenne dans les colonnes d'embeddings", "warning")
                stds = df[embed_cols].std(axis=1)
                low_variance_ratio = (stds < 1e-4).mean()
                if low_variance_ratio > 0.1:
                    self.logger.send_log(f"⚠️ - {uid} - {low_variance_ratio:.1%} des vecteurs d'embeddings ont une variance très faible", "warning")

                df.drop(columns=["rss_window", "content_embedding"], inplace=True)
                return df

            except Exception as e:
                self.logger.send_log(f"❌ - {uid} - Erreur dans merge_rss_data : {str(e)}", "error")
                return df

    def preprocess_data(self, data, uid):
        """
        Vérifie la continuité des timestamps avec une tolérance et évite l'interpolation artificielle.
        """
        df = pd.DataFrame(data)
        


        if "created_at" not in df.columns or "lastPrice" not in df.columns:
            self.logger.send_log(f"❌ - {uid} - Les données doivent contenir 'created_at' et 'lastPrice'.", "error")
            return None

        # Conversion en datetime
        df["timestamp"] = pd.to_datetime(df["created_at"])
        #df["time_marker"] = df["timestamp"]

        # Tri et suppression des doublons
        df = df.sort_values("timestamp").drop_duplicates("timestamp")

        # Remplacer les volumes nuls
        df.loc[df["volume"] == 0, "volume"] = 1

        # Calcul du délai entre timestamps
        df["time_diff"] = df["timestamp"].diff().dt.total_seconds().fillna(0)


        # Variation artificielle si prix constants
        if df["lastPrice"].nunique() == 1:
            df.loc[df.index[0], "lastPrice"] += np.random.uniform(-0.000001, 0.000001)

        # Nettoyage
        df.drop(columns=["time_diff", "created_at"], errors="ignore", inplace=True)

        if df["lastPrice"].isnull().any():
            self.log(f"⚠️ - {uid} - Des valeurs NaN détectées dans 'lastPrice'", "warning")

        df["lastPrice"] = df["lastPrice"].astype(float)

        return df
    


    def check_dataframe(self, df, uid, horizon_name):
        base_log = f"{uid} - {horizon_name}"

        try:
            # ✅ Dimensions et colonnes
            self.logger.send_log(f"📊 - {base_log} - Shape : {df.shape}", "debug")
            self.logger.send_log(f"📊 - {base_log} - Colonnes : {list(df.columns)}", "debug")

            # ✅ Types
            if df.dtypes.eq("object").any():
                self.logger.send_log(f"⚠️ - {base_log} - Colonnes avec type `object` détectées : {df.dtypes[df.dtypes == 'object'].index.tolist()}", "warning")

            # ✅ Timestamp (régularité et ordre)
            if "timestamp" in df.columns:
                diffs = pd.to_datetime(df['timestamp']).diff().dropna()
                irregular = diffs.value_counts().shape[0] > 1
                if irregular:
                    self.logger.send_log(f"⚠️ - {base_log} - Timestamps non réguliers détectés.", "warning")
                else:
                    self.logger.send_log(f"✅ - {base_log} - Timestamps réguliers.", "debug")
            else:
                self.logger.send_log(f"❌ - {base_log} - Colonne `timestamp` absente.", "error")

            # ✅ Vérification alignement log_return
            if {"price_now", "price_future", "log_return"}.issubset(df.columns):
                computed_return = np.log(df["price_future"] / df["price_now"])
                diff = computed_return - df["log_return"]
                max_error = np.abs(diff).max()
                if max_error > 1e-6:
                    self.logger.send_log(f"❌ - {base_log} - Mauvais alignement `log_return` détecté (max erreur = {max_error:.2e})", "error")
                else:
                    self.logger.send_log(f"✅ - {base_log} - Alignement `log_return` cohérent.", "debug")
            else:
                self.logger.send_log(f"⚠️ - {base_log} - Impossible de vérifier `log_return` (colonnes manquantes).", "warning")

            # ✅ Statistiques log_return
            if "log_return" in df.columns:
                log_stats = df["log_return"].describe()
                prop_zero = np.mean(np.abs(df["log_return"]) < 1e-4)
                self.logger.send_log(f"📈 - {base_log} - log_return : std={log_stats['std']:.6f}, min={log_stats['min']:.6f}, max={log_stats['max']:.6f}, prop_zero={prop_zero:.2%}", "debug")
            else:
                self.logger.send_log(f"❌ - {base_log} - Colonne `log_return` absente.", "error")

            prop_pos = (df["log_return"] > 0).mean()
            self.logger.send_log(f"📉 - {base_log} - Répartition direction : +{prop_pos:.1%} / -{1 - prop_pos:.1%}", "debug")

            # ✅ NaN / inf
            nulls = df.isna().sum().sum()
            numeric_df = df.select_dtypes(include=[np.number])
            infs = np.isinf(numeric_df.values).sum()
            self.logger.send_log(f"🔎 - {base_log} - NaN = {nulls}, Inf = {infs}", "debug")

            # ✅ Colonnes constantes ou quasi constantes
            constant_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col]) and df[col].std() < 1e-6]
            if constant_cols:
                self.logger.send_log(f"⚠️ - {base_log} - Colonnes constantes ou quasi constantes : {constant_cols}", "warning")

            # ✅ Valeurs extrêmes
            extreme_cols = df.select_dtypes(include=[np.number]).abs().max()
            suspicious = extreme_cols[extreme_cols > 1e3]
            if not suspicious.empty:
                self.logger.send_log(f"⚠️ - {base_log} - Colonnes avec valeurs extrêmes : {suspicious.to_dict()}", "warning")

        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - Erreur dans check_dataframe : {str(e)}", "error")


    def detect_features_to_normalize(self, df, uid, horizon_name, threshold_std=100, threshold_max=1000):
        base_log = f"{uid} - {horizon_name}"
        try:
            numeric_df = df.select_dtypes(include=[np.number])
            feature_stats = numeric_df.describe().T

            candidates_by_std = feature_stats[feature_stats['std'] > threshold_std].index.tolist()
            candidates_by_max = feature_stats[feature_stats['max'].abs() > threshold_max].index.tolist()

            combined_candidates = sorted(set(candidates_by_std + candidates_by_max))

            if combined_candidates:
                self.logger.send_log(
                    f"📐 - {base_log} - Features à scaler ou normaliser (std > {threshold_std} ou max > {threshold_max}) : {combined_candidates}",
                    "warning"
                )
            else:
                self.logger.send_log(
                    f"✅ - {base_log} - Toutes les features numériques ont des valeurs contenues",
                    "debug"
                )

        except Exception as e:
            self.logger.send_log(f"❌ - {base_log} - Erreur dans detect_features_to_normalize : {str(e)}", "error")



        
        


    def generate_indicator_deltas(self, df, indicator_source, indicator_period, period):
        """
        Génère des variations temporelles pour un indicateur donné et trace où les NaN apparaissent.
        """
        if indicator_source not in df.columns:
            raise ValueError(f"L'indicateur '{indicator_source}' n'existe pas dans le DataFrame")

        col_base = f"{indicator_source}_{indicator_period}"
        epsilon = 1e-8


        try:
            # Delta simple
            df[f"{col_base}_delta"] = df[indicator_source] - df[indicator_source].shift(-period)

            # Variation relative (%)
            df[f"{col_base}_pct"] = df[f"{col_base}_delta"] / (df[indicator_source].shift(-period) + epsilon)

            # Moyenne glissante
            df[f"{col_base}_mean"] = df[indicator_source].rolling(window=period).mean()

            return df

        except Exception as e:
            self.logger.send_log(f"❌ Erreur dans generate_indicator_deltas pour `{indicator_source}` ({indicator_period}) : {str(e)}", "error")
            raise



    def compute_period_indicators_safe(self, df, indicator_periods, uid, safe_shift_points):
        """
        Version sécurisée de compute_period_indicators : tous les indicateurs sont calculés sur lastPrice_past,
        une version décalée de lastPrice pour éviter toute fuite de données.
        
        safe_shift_points : int (nombre de points à décaler pour créer la base de prix sécurisée)
        """
        epsilon = 1e-6

        # 🔁 Création d'une base de prix sécurisée
        df["lastPrice_past"] = df["lastPrice"].shift(safe_shift_points)
        df["highPrice_past"] = df["highPrice"].shift(safe_shift_points)
        df["lowPrice_past"] = df["lowPrice"].shift(safe_shift_points)
        df["volume_past"] = df["volume"].shift(safe_shift_points)


        #df.to_html("/home/<USER>/debug_df/df_avant_tout.html")
        # ADX & ATR
        df["ADX"] = talib.ADX(df["highPrice_past"], df["lowPrice_past"], df["lastPrice_past"], timeperiod=indicator_periods["ADX"])
        df["ATR"] = talib.ATR(df["highPrice_past"], df["lowPrice_past"], df["lastPrice_past"], timeperiod=indicator_periods["ATR"])

        # RSI
        df["RSI"] = talib.RSI(df["lastPrice_past"], timeperiod=indicator_periods["RSI"])
        df["RSI_over_70"] = (df["RSI"] > 70).astype(int)
        #df = self.generate_indicator_deltas(df, "RSI", "medium", indicator_periods['RSI_delta_medium'])

        #df.to_html("/home/<USER>/debug_df/df_apres_premier_deltas.html")

        # Stochastic RSI
        fastk, fastd = talib.STOCHRSI(
            df["lastPrice_past"],
            timeperiod=indicator_periods["StochRSI_period"],
            fastk_period=indicator_periods["StochRSI_fastk"],
            fastd_period=indicator_periods["StochRSI_fastd"],
            fastd_matype=1
        )
        df["StochRSI_K"] = fastk
        df["StochRSI_D"] = fastd
        df["StochRSI_CrossUp"] = (
            (df["StochRSI_K"].shift(2) < df["StochRSI_D"].shift(2)) & 
            (df["StochRSI_K"].shift(1) > df["StochRSI_D"].shift(1))
        ).astype(int)

        # CMO
        df["CMO"] = talib.CMO(df["lastPrice_past"], timeperiod=indicator_periods["CMO"])

        # TEMA (scalé par lastPrice)
        df["TEMA"] = talib.TEMA(df["lastPrice_past"], timeperiod=indicator_periods["TEMA"])
        df["TEMA"] = df["TEMA"] / (df["lastPrice_past"] + epsilon)
        df["Price_TEMA_diff"] = df["lastPrice_past"] - df["TEMA"]

        # Sharpe Ratio local
        log_returns = np.log(df["lastPrice_past"] / df["lastPrice_past"].shift(1))
        rolling_mean = log_returns.rolling(window=indicator_periods["Sharpe_local"]).mean()
        rolling_std = log_returns.rolling(window=indicator_periods["Sharpe_local"]).std() + epsilon
        df["Sharpe_local"] = rolling_mean / rolling_std

        # MACD (scalé par ATR)
        macd, _, _ = talib.MACD(
            df["lastPrice_past"],
            fastperiod=indicator_periods["MACD_fast"],
            slowperiod=indicator_periods["MACD_slow"],
            signalperiod=indicator_periods["MACD_signal"]
        )
        df["MACD"] = macd
        df["MACD"] = df["MACD"] / (df["ATR"] + epsilon)
        df["MACD_cross_zero"] = (df["MACD"] > 0).astype(int)
        #df = self.generate_indicator_deltas(df, "MACD", "medium", indicator_periods['MACD_delta_medium'])

        #df.to_html("/home/<USER>/debug_df/df_apres_second_deltas.html")

        # EMA (scalé par lastPrice)
        ema_period = indicator_periods["EMA"]
        df["EMA"] = talib.EMA(df["lastPrice_past"], timeperiod=ema_period)
        df["EMA"] = df["EMA"] / (df["lastPrice_past"] + epsilon)
        upper, _, lower = talib.BBANDS(df["lastPrice_past"], timeperiod=ema_period)
        df["Percent_B"] = (df["lastPrice_past"] - lower) / ((upper - lower) + epsilon)

        # CMF
        df["PriceRange"] = df["highPrice_past"] - df["lowPrice_past"]
        df["PriceRange"] = df["PriceRange"].replace(0, epsilon)
        df["MoneyFlowMultiplier"] = ((df["lastPrice_past"] - df["lowPrice_past"]) - (df["highPrice_past"] - df["lastPrice_past"])) / df["PriceRange"]
        df["MoneyFlowVolume"] = df["MoneyFlowMultiplier"] * df["volume_past"]
        df["VolumeSum"] = df["volume_past"].rolling(window=indicator_periods["CMF"]).sum().replace(0, epsilon)
        df["CMF"] = df["MoneyFlowVolume"].rolling(window=indicator_periods["CMF"]).sum() / df["VolumeSum"]

        # CCI (scalé par ATR)
        df["CCI"] = talib.CCI(df["highPrice_past"], df["lowPrice_past"], df["lastPrice_past"], timeperiod=indicator_periods["CCI"])
        df["CCI"] = df["CCI"] / (df["ATR"] + epsilon)

        # MFI
        if df["volume_past"].sum() == 0:
            df["MFI"] = 50
        else:
            df["MFI"] = talib.MFI(df["highPrice_past"], df["lowPrice_past"], df["lastPrice_past"], df["volume_past"], timeperiod=indicator_periods["MFI"])

        # VWAP (scalé)
        df["TypicalPrice"] = (df["highPrice_past"] + df["lowPrice_past"] + df["lastPrice_past"]) / 3
        df["PV"] = df["TypicalPrice"] * df["volume_past"]
        df["RollingPV"] = df["PV"].rolling(window=indicator_periods["VWAP"]).sum()
        df["RollingVolume"] = df["volume_past"].rolling(window=indicator_periods["VWAP"]).sum().replace(0, epsilon)
        df["VWAP"] = df["RollingPV"] / df["RollingVolume"]
        df["VWAP"] = df["VWAP"] / (df["lastPrice_past"] + epsilon)
        df["VWAP_Diff"] = df["lastPrice_past"] - df["VWAP"]
        df["VWAP_Diff"] = df["VWAP_Diff"] / (df["lastPrice_past"] + epsilon)

        # Z-score
        z_mean = df["lastPrice_past"].rolling(window=indicator_periods["Z_Score"]).mean()
        z_std = df["lastPrice_past"].rolling(window=indicator_periods["Z_Score"]).std().replace(0, epsilon)
        df["Z_Score"] = (df["lastPrice_past"] - z_mean) / z_std
        df["Z_Score_abs"] = df["Z_Score"].abs()

        # Momentum (scalé par lastPrice)
        df["Momentum"] = df["lastPrice_past"].diff(periods=indicator_periods["Momentum"])
        df["Momentum"] = df["Momentum"] / (df["lastPrice_past"] + epsilon)
        #df = self.generate_indicator_deltas(df, "Momentum", "medium", indicator_periods['Momentum_delta_medium'])

        # Cross-features
        df["MACD_ATR"] = df["MACD"] / (df["ATR"] + epsilon)
        df["RSI_ADX"] = df["RSI"] * df["ADX"] / 100
        df["CCI_ATR"] = df["CCI"] / (df["ATR"] + epsilon)
        df["MFI_RSI"] = (df["MFI"] + df["RSI"]) / 2
        df["Momentum_ADX"] = df["Momentum"] * (df["ADX"] / 100)
        df["RSI_TEMA_diff"] = df["RSI"] - df["TEMA"]
        df["ZScore_RSI"] = df["Z_Score"] * df["RSI"] / 100
        df["VWAP_TEMA_ratio"] = df["VWAP"] / (df["TEMA"] + epsilon)
        df["StochRSI_TEMA"] = df["StochRSI_K"] * df["TEMA"] / (df["lastPrice_past"] + epsilon)

        # Volume
        df["log_volume"] = np.log(df["volume_past"] + 1e-6)

        # Clean
        df.drop(columns=[
            "volumeSum", "volume", "PriceRange", "MoneyFlowMultiplier", "MoneyFlowVolume",
            "TypicalPrice", "PV", "RollingPV", "RollingVolume",
            "RSI_over_70", "MACD_cross_zero", "Price_TEMA_diff", "lastPrice_past", "highPrice_past", "lowPrice_past", "volume_past"
        ], errors="ignore", inplace=True)

        #df.to_html("/home/<USER>/debug_df/df_fin_generation.html")
        del log_returns, rolling_mean, rolling_std
        del fastk, fastd
        del upper, lower
        gc.collect()

        return df
    

    def verify_temporal_alignment(self, df, shift, safe_shift_points, log_col="log_return", uid=None):
        """
        Vérifie que toutes les colonnes sont temporellement alignées :
        - Les features à t doivent être construites avec des données ≤ t - safe_shift_points
        - La target à t doit correspondre à une variation t - safe_shift_points → t - safe_shift_points + shift
        """
        errors = []
        index = df.index

        df.drop(columns=[
            "highPrice", "lowPrice",
            "price_now", "price_future",
            "lastPrice_past", "highPrice_past", "lowPrice_past", "volume_past"
        ], errors="ignore", inplace=True)

        # Recalcule les prix associés à la target (logiquement déjà fait, mais à revérifier ici)
        expected_price_now = df["lastPrice"].shift(safe_shift_points)
        expected_price_future = df["lastPrice"].shift(safe_shift_points - shift)
        expected_log_return = np.log(expected_price_future / expected_price_now)

        # Vérifie que chaque log_return est cohérente avec la formule
        diff = np.abs(df[log_col] - expected_log_return)
        if (diff > 1e-8).any():
            errors.append("❌ Target log_return mal alignée")

        # Vérifie que toutes les colonnes features ne sont pas postérieures à `timestamp - safe_shift_points`
        # Hypothèse : `df["time_marker"]` contient l'instant t, et features sont à `t - safe_shift_points`
        if "time_marker" in df.columns:
            for col in df.columns:
                if col in ["log_return", "time_marker", "timestamp", "lastPrice"]:
                    continue
                # Extrait une colonne et vérifie la corrélation croisée avec un prix trop récent
                if df[col].isna().all():
                    continue
                lagged_corr = df[col].corr(df["lastPrice"])  # Ne prouve rien, mais indicateur
                if lagged_corr > 0.9:
                    errors.append(f"⚠️ Feature suspecte : {col} semble corrélée à lastPrice brut")
        else:
            errors.append("🟡 time_marker manquant pour vérification stricte")

        if errors:
            self.logger.send_log("\n".join(errors), "debug")
            return False
        return True




    def generate_training_sample(self, raw_data, indicator_periods, uid, shift, horizon_hour, depth=None, safe_shift_points=0):
        """
        Génère un DataFrame d'entraînement avec toutes les features calculées à partir 
        d'une base sécurisée (décalée dans le passé), et la target log_return basée sur
        log(price_t+shift / price_t).
        """
        df = None
        abs_log_return = None

        if safe_shift_points != 0:
            self.logger.send_log(f"❌ - {uid} - Safe shift point n'est pas égal à 0", "error")

        try:
            # Prétraitement initial
            df = self.preprocess_data(raw_data, uid)
            if df is None or df.empty:
                self.logger.send_log(f"❌ - {uid} - Données vides après prétraitement", "error")
                return None

            df = self.compute_period_indicators_safe(df, indicator_periods, uid, safe_shift_points)
            if df is None:
                self.logger.send_log(f"❌ - {uid} - Échec du calcul des indicateurs", "error")
                return None

            #df.to_html(f"/home/<USER>/debug_df/df_{uid}_{horizon_hour}h_avant_log_return.html")
            # Ajout RSS
            #df = self.merge_rss_data(df, crypto=crypto, uid=uid)

            # Nettoyage initial
            df.dropna(inplace=True)
            df.sort_values("timestamp", inplace=True)
            df.reset_index(drop=True, inplace=True)

            # === Target : log_return = log(price_t+shift / EMA(price_now))
            df["price_now"]   = df["lastPrice"]
            df["price_future"]= df["lastPrice"].shift(-shift)
            df.dropna(subset=["price_now","price_future"], inplace=True)
            df["log_return"]  = np.log(df["price_future"] / df["price_now"])

            '''
            # === Target : log_return = log(price_t+shift / price_t)
            df["price_now"] = df["lastPrice"].shift(safe_shift_points)
            df["price_future"] = df["lastPrice"].shift(safe_shift_points - shift)
            df.dropna(subset=["price_now", "price_future"], inplace=True)

            df["log_return"] = np.log(df["price_future"] / df["price_now"])
            '''

            '''
            ok = self.verify_temporal_alignment(df, shift=shift, safe_shift_points=safe_shift_points, uid=uid)
            if not ok:
                self.logger.send_log(f"❌ - {uid} - Vérification d'alignement temporel échouée", "error")
                return None
            '''

            
            # Rolling smoothing léger avant suppression
            #df["log_return"] = df["log_return"].rolling(window=horizon_hour, min_periods=1).mean()

            # Suppression de log_returns entrêmes
            abs_log_return = np.abs(df["log_return"])

            '''
            if horizon_hour <= 6:
                quantile_threshold = min(0.06 / horizon_hour, 0.02)
            elif horizon_hour <= 24:
                quantile_threshold = 0.005  # léger nettoyage
            else:
                quantile_threshold = 0.002  # nettoyage très doux

            threshold = abs_log_return.quantile(quantile_threshold)
            df.loc[abs_log_return < threshold, "log_return"] = 0
            '''
            
            #upper_clip = df["log_return"].abs().quantile(0.995)
            #df["log_return"] = df["log_return"].clip(lower=-upper_clip, upper=upper_clip)
            

            df.dropna(subset=["log_return"], inplace=True)
            df.reset_index(drop=True, inplace=True)

            # Suppression des colonnes temporaires
            df.drop(columns=["price_now", "price_future", "time_marker", "price_now_ema"], inplace=True, errors="ignore")

            # Fonction de check du dataframe
            #self.check_dataframe(df_sorted, uid=uid, horizon_name=f"{horizon_hour}h")
            #self.detect_features_to_normalize(df_sorted, uid=uid, horizon_name=f"{horizon_hour}h")


            # Limitation de profondeur (tail)
            if depth is None:
                max_indicator_period = max(indicator_periods.values())
                buffer_points = 10
                required_tail = (horizon_hour * 60) + shift + max_indicator_period + buffer_points
                df = df.tail(required_tail)
            elif depth != "max":
                df = df.tail(depth)

            # Diagnostic NaN
            if df.isna().sum().sum() > 0:
                nan_cols = df.columns[df.isna().any()].tolist()
                self.logger.send_log(f"⚠️ Colonnes contenant des NaN : {nan_cols}", "warning")
                self.logger.send_log(f"🧪 Dtypes du df : {df.dtypes.to_dict()}", "debug")
                self.logger.send_log(f"🔍 Nombre total de NaN : {df.isna().sum().sum()}", "debug")
                self.logger.send_log(f"🧼 NaN par colonne : {df.isna().sum().to_dict()}", "debug")

            
            #df.to_html(f"/home/<USER>/debug_df/df_{uid}_{horizon_hour}h_apres_log_return.html")

            return df

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur dans generate_training_sample : {str(e)}", "error")
            return None

        finally:
            del df
            gc.collect()



    def generate_prediction_sample(self, raw_data, indicator_periods, uid, sequence_length=64, safe_shift_points=1):
        """
        Prépare un DataFrame pour la prédiction, avec les indicateurs calculés,
        en sélectionnant uniquement les `sequence_length` dernières lignes pour une
        prédiction séquentielle type LSTM/CNN.
        """
        try:
            # 1. Prétraitement initial
            df = self.preprocess_data(raw_data, uid)
            if df is None or df.empty:
                self.logger.send_log(f"❌ - {uid} - Données vides après prétraitement", "error")
                return None

            # 2. Ajout des données RSS (optionnel mais présent dans ta logique)
            #df = self.merge_rss_data(df, crypto=crypto, uid=uid)

            # 3. Calcul des indicateurs
            df = self.compute_period_indicators_safe(df, indicator_periods, uid, safe_shift_points)
            if df is None:
                self.logger.send_log(f"❌ - {uid} - Échec du calcul des indicateurs", "error")
                return None

            # 4. Nettoyage
            df = df.dropna().sort_values("timestamp").reset_index(drop=True)

            # 5. Suppression des colonnes inutiles à la prédiction
            df.drop(columns=["timestamp", "highPrice", "lowPrice", "lastPrice"], inplace=True, errors="ignore")

            # 6. Vérification de NaN
            if df.isna().sum().sum() > 0:
                nan_cols = df.columns[df.isna().any()].tolist()
                self.logger.send_log(f"⚠️ - {uid} - NaN détectés dans colonnes : {nan_cols}", "warning")
                self.logger.send_log(f"🔍 Détails : {df.isna().sum().to_dict()}", "debug")

            # 7. Vérification de taille de la séquence
            if len(df) < sequence_length:
                self.logger.send_log(f"⚠️ - {uid} - Pas assez de points pour la prédiction ({len(df)}/{sequence_length})", "warning")
                return None

            return df.tail(sequence_length)

        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur dans generate_prediction_sample : {str(e)}", "error")
            return None


    


    def generate_prediction_sample_safe(self, raw_data, indicator_periods, uid, shift, crypto):
        """
        Prépare un DataFrame pour la prédiction, avec les indicateurs calculés 
        sur une version sécurisée de lastPrice (shiftée pour éviter toute fuite de données).
        """
        try:
            # Prétraitement initial
            df = self.preprocess_data(raw_data, uid)
            if df is None or df.empty:
                self.logger.send_log(f"❌ - {uid} - Données vides après prétraitement", "error")
                return None
            df = self.merge_rss_data(df, crypto=crypto, uid=uid)

            safe_shift_points = shift
            df = self.compute_period_indicators_safe(df, indicator_periods, uid, safe_shift_points)

            # Utilisation d'une version shiftée de lastPrice pour sécuriser les indicateurs
            safe_shift_points = shift  # ✅ Même logique que dans generate_training_sample
            df = self.compute_period_indicators_safe(df, indicator_periods, uid, safe_shift_points)
            if df is None:
                self.logger.send_log(f"❌ - {uid} - Échec du calcul des indicateurs", "error")
                return None
            
            min_required_points = safe_shift_points + max(indicator_periods.values())
            if len(df) < min_required_points:
                self.logger.send_log(
                    f"⚠️ - {uid} - Données insuffisantes pour prédiction (min requis = {min_required_points}, dispo = {len(df)})",
                    "warning"
                )
                return None

            # Nettoyage
            df = df.dropna().reset_index(drop=True)
            df = df.sort_values("timestamp").reset_index(drop=True)

            # Suppression des colonnes inutiles
            df.drop(columns=["timestamp", "highPrice", "lowPrice", "lastPrice"], inplace=True, errors="ignore")

            # Vérification NaN
            if df.isna().sum().sum() > 0:
                nan_cols = df.columns[df.isna().any()].tolist()
                self.logger.send_log(f"⚠️ - {uid} - NaN détectés dans colonnes : {nan_cols}", "warning")
                self.logger.send_log(f"🔍 Détails : {df.isna().sum().to_dict()}", "debug")

            # On prend uniquement le dernier point pour prédire
            return df
    
        except Exception as e:
            self.logger.send_log(f"❌ - {uid} - Erreur dans generate_prediction_sample_safe : {str(e)}", "error")
            return None



